.container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 100px 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.title {
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 0.5rem;
  font-size: 2.2rem;
}

.subtitle {
  color: #666;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.2rem;
  font-weight: normal;
}

.errorMessage {
  background-color: #fff3f3;
  color: #d32f2f;
  padding: 0.8rem;
  border-radius: 5px;
  margin-bottom: 1.5rem;
  border-left: 4px solid #d32f2f;
}

.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.controlGroup {
  flex: 1;
  min-width: 200px;
}

.controlGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.select {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  background-color: #f9f9f9;
  font-size: 1rem;
}

.textInput {
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  margin-bottom: 1.5rem;
  resize: vertical;
  min-height: 150px;
}

.buttonGroup {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.button {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #333;
}

.button:hover {
  background-color: #e0e0e0;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.primary {
  background-color: var(--primary-color);
  color: white;
}

.primary:hover {
  background-color: var(--secondary-color);
}

.secondary {
  background-color: var(--hover-color);
  color: #333;
}

.secondary:hover {
  background-color: var(--last-color);
}

.audioControl {
  width: 100%;
  margin-bottom: 1.5rem;
  border-radius: 30px;
}

.infoBox {
  background-color: #f8f9fa;
  padding: 1.2rem;
  border-radius: 5px;
  border-left: 4px solid var(--primary-color);
}

.infoBox p {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.infoBox ul {
  margin: 0;
  padding-left: 1.5rem;
}

.infoBox li {
  margin-bottom: 0.5rem;
  color: #555;
}

@media (max-width: 600px) {
  .container {
    padding: 1.5rem;
    margin: 1rem;
  }
  
  .buttonGroup {
    flex-direction: column;
  }
  
  .button {
    width: 100%;
  }
}


