/* Container chính */
.container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  overflow-y: auto; /* Chỉ cho phép scroll trong container này */
}

/* Scrollbar tuỳ chọn */
.container::-webkit-scrollbar {
  width: 6px;
}
.container::-webkit-scrollbar-track {
  background: #f5f5f5;
}
.container::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}
.container::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}

/* Placeholder khi chưa chọn job */
.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 15px;
  color: #777;
  background-color: #f9f9f9;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Header */
.header {
  display: flex;
  align-items: center;
  padding-bottom: 14px; /* Gi<PERSON>m padding */
  border-bottom: 1px solid #f0f0f0;
}

.logoWrapper {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.companyLogo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.companyInitial {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  color: #6c00ff;
  background-color: rgba(108, 0, 255, 0.1);
}

.info {
  flex: 1;
  margin-left: 16px;
}

.companyName {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.positionTitle {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: #6c00ff;
}

.location {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* Bookmark button */
.bookmarkBtn {
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 20px;
  color: #6c00ff;
  margin-left: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.bookmarkBtn:hover {
  background-color: rgba(108, 0, 255, 0.1);
}

/* Tags row */
.tagsRow {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* Mỗi tag chung */
.tag {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  padding: 8px 12px;
  flex: 1;
  min-width: 100px;
}

/* Label nhỏ bên trên */
.tagLabel {
  font-size: 11px;
  color: #666;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Value to hơn */
.tagValue {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* Màu nền cho từng tag */
.salaryTag {
  background-color: #e6f7ee;
}
.jobTypeTag {
  background-color: #e6f0ff;
}
.applicantsTag {
  background-color: #fff0e6;
}
.skillTag {
  background-color: #f0e6ff;
}

/* Nội dung */
.contentArea {
  display: flex;
  flex-direction: column;
  gap: 16px; /* Giảm khoảng cách */
  flex: 1;
  overflow-y: visible; /* Cho phép scroll trong container cha */
}

/* Section */
.section {
  display: flex;
  flex-direction: column;
  gap: 6px; /* Giảm khoảng cách */
}

.sectionTitle {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #333;
  padding-bottom: 4px; /* Giảm padding */
  border-bottom: 1px solid #f0f0f0;
}

/* Danh sách bullet */
.list {
  list-style: disc;
  padding-left: 20px;
  margin: 0;
}

.listItem {
  font-size: 14px;
  color: #555;
  margin-bottom: 6px;
  line-height: 1.5;
}

/* Apply Now */
.applyRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.verifiedBadge {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #28a745;
  font-weight: 500;
}

.verifiedIcon {
  font-size: 16px;
}

.applyButton {
  background-color: #6c00ff;
  color: #fff;
  border: none;
  padding: 10px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.applyButton:hover {
  background-color: #5500cc;
}

.description {
  font-size: 14px;
  line-height: 1.6;
  color: #555;
  overflow-y: auto;
  max-height: none; /* Bỏ giới hạn chiều cao */
  padding-right: 10px;
}

/* Styling cho nội dung HTML từ API */
.description h1,
.description h2,
.description h3,
.description h4,
.description h5,
.description h6 {
  margin-top: 14px;
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
}

.description h1 {
  font-size: 20px;
}

.description h2 {
  font-size: 18px;
}

.description h3 {
  font-size: 16px;
}

.description p {
  margin-bottom: 10px;
}

.description ul,
.description ol {
  margin-left: 20px;
  margin-bottom: 10px;
}

.description li {
  margin-bottom: 4px;
}

.description a {
  color: #6c00ff;
  text-decoration: none;
}

.description a:hover {
  text-decoration: underline;
}

.description pre,
.description code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: monospace;
}

/* Scrollbar styling cho description */
.description::-webkit-scrollbar {
  width: 4px;
}

.description::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 10px;
}

.description::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 10px;
}

.description::-webkit-scrollbar-thumb:hover {
  background: #ccc;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 16px;
    gap: 14px;
  }

  .tagsRow {
    flex-wrap: wrap;
  }

  .tag {
    min-width: calc(50% - 5px);
    flex: 0 0 calc(50% - 5px);
  }

  .description {
    max-height: 250px;
  }
}
