/* src/components/JobsSection/JobsSection.module.css */

.section {
  padding: 20px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.loading,
.error {
  text-align: center;
  font-size: 16px;
  color: #666;
}

/* Hint text above tabs */
.hint {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 18px;
  color: var(--secondary-color);
  margin-bottom: 16px;
  height: 30px;
}

/* Tab buttons container */
.tabs {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  margin-bottom: 24px;
}

/* Individual tab button */
.tabButton {
  padding: 8px 16px;
  border: 1px solid #ccc;
  border-radius: 24px;
  background: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s, color 0.2s, border-color 0.2s;
}

.tabButton:hover {
  background: #f0f0f0;
}

/* Active tab styling */
.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

/* Grid layout for cards */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  /* thêm dòng này để mỗi row đều cao bằng nhau */
  grid-auto-rows: 1fr;

  gap: 24px;
  transition: opacity 0.25s;
  opacity: 1;
}

/* Fade in/out effects */
.fadeIn {
  opacity: 1;
  pointer-events: auto;
}

.fadeOut {
  opacity: 0;
  pointer-events: none;
}

/* Single card */
.card {
  display: flex; /* khôi phục flex */
  flex-direction: column;
  justify-content: space-between;

  height: 280px; /* cố định height (tùy chỉnh nếu cần) */
  overflow: hidden; /* ẩn phần tràn */

  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}
.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Card header: logo, date, bookmark */
.cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
}
.posted {
  font-size: 12px;
  color: #888;
}

.bookmark {
  font-size: 18px;
  color: #ffb400;
  margin-left: 8px;
}

/* Company name */
.company {
  font-size: 15px;
  color: var(--primary-color);
  margin: 0 0 4px 0;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* Job title */
.title {
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: #222;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* Tags container */
.tags {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;

  white-space: nowrap;
  overflow: hidden;
}

/* Individual tag */
.tag {
  background: #f3f3f3;
  color: var(--primary-color);
  border-radius: 12px;
  padding: 2px 10px;
  font-size: 12px;

  display: inline-block;
  max-width: 100px; /* hoặc chỉnh theo ý bạn */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Card footer: salary/location and button */
.cardFooter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12px;
}

.salary {
  font-weight: bold;
  color: #222;
}

.location {
  font-size: 13px;
  color: #888;
}

/* Details button */
.detailsBtn {
  background: var(--secondary-color);
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 6px 18px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.detailsBtn:hover {
  background: #4b00b3;
}

/* Pagination styles */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 32px;
  gap: 16px;
}

.pageBtn {
  padding: 8px 18px;
  border: 1px solid #ccc;
  border-radius: 24px;
  background: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s, color 0.2s, border-color 0.2s;
}

.pageBtn:disabled {
  background: #eee;
  color: #aaa;
  cursor: not-allowed;
}

.pageInfo {
  font-size: 15px;
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .grid {
    grid-template-columns: 1fr;
  }
  .card {
    padding: 12px;
  }
  .pagination {
    flex-direction: column;
    gap: 8px;
  }
}