/* PostList.module.css */

.postList {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: visible; /* Ensure dropdowns can appear outside container */
}

.postItem {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  overflow: visible; /* Changed from hidden to visible */
  transition: all 0.3s ease;
  position: relative; /* Add position relative */
}

.postItem:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* Post Header */
.postHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 20px 0;
}

.authorInfo {
  display: flex;
  gap: 12px;
  flex: 1;
}

.authorAvatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #FF6701 0%, #FF8533 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.authorAvatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(255, 103, 1, 0.3);
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatarIcon {
  font-size: 20px;
}

.authorDetails {
  flex: 1;
  min-width: 0;
}

.authorName {
  font-weight: 600;
  font-size: 15px;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 2px;
}

.verifiedBadge {
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.postMeta {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 13px;
}

.postTime {
  color: #6b7280;
}

.visibilityIcon {
  font-size: 12px;
}

.moreButtonContainer {
  position: relative;
  z-index: 10; /* Add z-index to ensure dropdown appears above other elements */
}

.moreBtn {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 18px;
  cursor: pointer;
  padding: 8px; /* Increase padding for better touch target */
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px; /* Ensure minimum clickable area */
  min-height: 36px;
}

.moreBtn:hover {
  background: #f3f4f6;
  color: #374151;
  transform: scale(1.05); /* Slight scale on hover */
}

.moreBtn:active {
  transform: scale(0.95); /* Scale down on click */
}

/* Dropdown Menu */
.dropdownMenu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2); /* Increase shadow for better visibility */
  z-index: 1001; /* Increase z-index */
  min-width: 200px;
  padding: 8px 0;
  margin-top: 4px;
  transform: translateY(0); /* Ensure proper positioning */
  opacity: 1;
  visibility: visible;
  animation: dropdownSlideIn 0.15s ease-out;
}

@keyframes dropdownSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-8px);
    visibility: hidden;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
  }
}

.dropdownItem {
  width: calc(100% - 16px);
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border: none;
  background: none;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  margin: 0 8px;
  border-radius: 8px;
}

.dropdownItem:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.dropdownItem.active {
  background: #eff6ff;
  color: var(--primary-color);
}

.dropdownIcon {
  font-size: 16px;
  flex-shrink: 0;
}

.dropdownDivider {
  height: 1px;
  background: #e5e7eb;
  margin: 8px 0;
}

.dropdownLabel {
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.checkmark {
  margin-left: auto;
  color: #10b981;
  font-weight: 600;
}

/* Post Content */
.postContent {
  padding: 12px 20px 0;
}

.postText {
  font-size: 15px;
  line-height: 1.5;
  color: #1f2937;
  margin-bottom: 12px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.postMedia {
  margin-top: 12px;
  border-radius: 12px;
  overflow: hidden;
  display: grid;
  gap: 8px;
  grid-template-columns: 1fr;
}

/* Multiple images grid layout */
.postMedia:has(.mediaItem:nth-child(2)) {
  grid-template-columns: 1fr 1fr;
}

.postMedia:has(.mediaItem:nth-child(3)) {
  grid-template-columns: 1fr 1fr;
}

.postMedia:has(.mediaItem:nth-child(4)) {
  grid-template-columns: 1fr 1fr;
}

.postMedia .mediaItem:first-child:nth-last-child(3) {
  grid-column: 1 / -1;
}

.mediaItem {
  position: relative;
  width: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.postImage {
  width: 100%;
  height: 100%;
  min-height: 200px;
  max-height: 400px;
  object-fit: cover;
  background: #f8f9fa;
  cursor: pointer;
  transition: transform 0.2s ease;
  display: block;
}

/* Single image styling */
.postMedia .mediaItem:only-child .postImage {
  max-height: 500px;
  object-fit: contain;
}

.postImage:hover {
  transform: scale(1.01);
}

/* Error placeholder for failed images */
.imageErrorPlaceholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  color: #6b7280;
  font-size: 1.1rem;
  font-weight: 500;
}

.postTags {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  color: #3b82f6;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease;
}

.tag:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

/* Post Stats */
.postStats {
  padding: 8px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
  margin-top: 12px;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 4px;
}

.statCount {
  font-size: 13px;
  color: #6b7280;
  cursor: pointer;
  transition: color 0.2s ease;
}

.statCount:hover {
  color: #374151;
  text-decoration: underline;
}

/* Post Actions */
.postActions {
  display: flex;
  padding: 8px 12px;
  gap: 4px;
}

.actionBtn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 12px;
  background: none;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionBtn:hover {
  background: #f3f4f6;
  color: #374151;
}

.actionBtn.liked {
  color: #ef4444;
}

.actionBtn.liked:hover {
  background: #fef2f2;
  color: #dc2626;
}

.heartIcon, .actionIcon {
  font-size: 16px;
}

.actionBtn.liked .heartIcon {
  color: #ef4444;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6b7280;
}

.loadingIcon {
  font-size: 24px;
  margin-bottom: 12px;
  animation: spin 1s linear infinite;
}

.spinIcon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #6b7280;
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.emptyState h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.emptyState p {
  font-size: 14px;
  color: #6b7280;
}

/* Load More */
.loadMoreContainer {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.loadMoreBtn {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 12px 24px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.loadMoreBtn:hover {
  border-color: #FF6701;
  color: #FF6701;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 103, 1, 0.15);
}

.loadMoreBtn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

/* Job Item Styles */
.jobItem {
  border-left: 4px solid #FF6701;
  background: linear-gradient(135deg, #fefefe 0%, #f9f9f9 100%);
}

.jobItem:hover {
  box-shadow: 0 6px 20px rgba(255, 103, 1, 0.15);
}

.jobBadge {
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #FF6701 0%, #FF8533 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.jobBadge svg {
  font-size: 14px;
}

.jobContent {
  padding: 0 20px 16px;
}

.jobTitle {
  font-size: 20px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  line-height: 1.3;
}

.jobDetails {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.jobDetailItem {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.jobIcon {
  color: #FF6701;
  font-size: 16px;
}

.jobRequirement {
  margin-bottom: 12px;
  color: #444;
  font-size: 14px;
  line-height: 1.5;
}

.jobRequirement strong {
  color: #1a1a1a;
  font-weight: 600;
}

.jobSkills {
  margin-bottom: 12px;
}

.jobSkills strong {
  color: #1a1a1a;
  font-weight: 600;
  font-size: 14px;
}

.skillTags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.skillTag {
  background: linear-gradient(135deg, #FF6701 0%, #FF8533 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.jobBenefits {
  margin-bottom: 12px;
}

.jobBenefits strong {
  color: #1a1a1a;
  font-weight: 600;
  font-size: 14px;
}

.benefitsList {
  list-style: none;
  padding: 0;
  margin: 8px 0 0 0;
}

.benefitsList li {
  color: #666;
  font-size: 14px;
  padding: 4px 0;
  position: relative;
  padding-left: 20px;
}

.benefitsList li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #FF6701;
  font-weight: bold;
}

.jobDeadline {
  color: #d73527;
  font-size: 14px;
  font-weight: 600;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
}

.jobDeadline strong {
  color: #d73527;
}

.jobActions {
  display: flex;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.primaryJobBtn {
  background: linear-gradient(135deg, #FF6701 0%, #FF8533 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  max-width: 150px;
}

.primaryJobBtn:hover {
  background: linear-gradient(135deg, #e55a01 0%, #e6732e 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 103, 1, 0.3);
}

.secondaryJobBtn {
  background: transparent;
  color: #FF6701;
  border: 2px solid #FF6701;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  max-width: 120px;
}

.secondaryJobBtn:hover {
  background: #FF6701;
  color: white;
  transform: translateY(-1px);
}

.benefitText {
  color: #666;
  font-size: 14px;
  margin-top: 8px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .postItem {
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .postHeader {
    padding: 12px 16px 0;
  }
  
  .postContent {
    padding: 8px 16px 0;
  }
  
  .postStats {
    padding: 8px 16px;
  }
  
  .postActions {
    padding: 8px 12px;
  }
  
  .actionBtn {
    padding: 6px 8px;
    font-size: 13px;
  }
  
  .postText {
    font-size: 14px;
  }
  
  .authorName {
    font-size: 14px;
  }

  /* Mobile image grid */
  .postMedia:has(.mediaItem:nth-child(2)) {
    grid-template-columns: 1fr;
  }

  .postMedia .mediaItem:first-child:nth-last-child(3) {
    grid-column: auto;
  }

  .postImage {
    min-height: 180px;
    max-height: 300px;
  }
  .postMedia .mediaItem:only-child .postImage {
    max-height: 400px;
  }

  .jobTitle {
    font-size: 18px;
  }
  
  .jobDetails {
    flex-direction: column;
    gap: 8px;
  }
  
  .jobActions {
    flex-direction: column;
    gap: 8px;
  }
  
  .primaryJobBtn,
  .secondaryJobBtn {
    max-width: none;
  }
  
  .skillTags {
    gap: 6px;
  }
  
  .skillTag {
    font-size: 11px;
    padding: 3px 8px;
  }

  /* Responsive dropdown adjustments */
  .dropdownMenu {
    position: fixed;
    top: auto;
    right: 16px;
    left: 16px;
    bottom: 16px;
    margin-top: 0;
    border-radius: 16px;
    min-width: auto;
    max-height: 50vh;
    overflow-y: auto;
    animation: dropdownSlideUp 0.2s ease-out;
  }
  
  @keyframes dropdownSlideUp {
    0% {
      opacity: 0;
      transform: translateY(100%);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .dropdownItem {
    padding: 12px 16px;
    font-size: 16px;
  }
  
  .moreButtonContainer {
    z-index: 10;
  }
}

/* Add backdrop for mobile dropdown */
.dropdownBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: none;
}

@media (max-width: 768px) {
  .dropdownBackdrop {
    display: block;
  }
}
