rules_version = '2';

// Firebase Storage Rules
service firebase.storage {
  match /b/{bucket}/o {
    // Images uploads
    match /posts/{userId}/{imageId} {
      // Allow read access to all authenticated users
      allow read: if request.auth != null;
      // Allow write access to owner
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Avatar uploads
    match /avatars/{userId}/{imageId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow all authenticated users to read any image (for now)
    match /{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
  }
}
