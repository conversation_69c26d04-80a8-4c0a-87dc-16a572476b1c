@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.testimonials {
  max-width: 1100px;
  margin: 0 auto;
  padding: 12px 20px;
  font-family: "Roboto", sans-serif;
  color: #1a1a1a;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}
.title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
}
.subtitle {
  font-size: 1rem;
  color: #555;
  margin-top: 8px;
  max-width: 800px;
  margin: 0 auto;
}

.carousel {
  position: relative;
  display: flex;
  align-items: center;
}
.arrow {
  background: #fff;
  border: 1px solid #ddd;
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1rem;
  transition: background 0.2s;
}
.arrow:hover {
  background: #f5f5f5;
}

.cardWrapper {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* apply sliding animations */
.slideInRight {
  animation: slideInRight 0.5s ease-out;
}
.slideInLeft {
  animation: slideInLeft 0.5s ease-out;
}

.card {
  display: flex;
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  max-width: 800px;
  width: 100%;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.avatarWrap {
  flex: 0 0 40%;
  background: #f5f5f5;
  position: relative;
}
.avatar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content {
  flex: 1;
  padding: 24px;
}
.rating {
  color: #f5a623;
  font-size: 1.125rem;
  margin-bottom: 16px;
}
.quote {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 24px;
}
.clientName {
  font-size: 1.125rem;
  font-weight: 700;
  margin: 0;
}
.clientRole {
  font-size: 0.875rem;
  color: #777;
  margin: 4px 0 0;
}
