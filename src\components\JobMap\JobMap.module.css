/* components/JobMap/JobMap.module.css */
.mapContainer {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* Đảm bảo Leaflet container c<PERSON> kích thước đúng */
.mapContainer :global(.leaflet-container) {
  width: 100% !important;
  height: 100% !important;
}

.loadingMap {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  font-size: 16px;
  color: #666;
}

.mapError {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #fff0f0;
  padding: 20px;
  text-align: center;
}

.mapError button {
  margin-top: 15px;
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.mapError button:hover {
  background-color: #357ab8;
}

.noJobsOverlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.8);
  padding: 15px 20px;
  border-radius: 8px;
  font-size: 16px;
  color: #333;
  text-align: center;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.popup {
  display: flex;
  flex-direction: column;
  align-items: start;
  gap: 4px;
  min-width: 200px;
}

.popupHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  width: 100%;
}

.popupLogo {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f0f0f0;
}

.logoImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.logoFallback {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #4a90e2;
  color: white;
  font-weight: bold;
  font-size: 18px;
  justify-content: center;
  align-items: center;
}

.companyName {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.title {
  margin: 0;
  font-size: 13px;
  color: #333;
}

.location {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.salary {
  font-size: 12px;
  font-weight: 500;
  color: #000;
}

.viewBtn {
  margin-top: 6px;
  padding: 6px 12px;
  font-size: 12px;
  background-color: #4a90e2;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.viewBtn:hover {
  background-color: #357ab8;
}
