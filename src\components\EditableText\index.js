import React, { useState, useEffect } from 'react';
import { Input } from 'antd';

const { TextArea } = Input;

const EditableText = ({
  isEditing,
  value,
  onChange,
  multiline = false,
  placeholder = "Nhập text..."
}) => {
  const [draft, setDraft] = useState(value);

  // If the component is not in editing mode, just display the value.
  if (!isEditing) {
    // Render the text with line breaks if it's multiline.
    if (multiline && typeof value === 'string') {
      return (
        <span style={{ whiteSpace: 'pre-wrap' }}>
          {value}
        </span>
      );
    }
    return <span>{value}</span>;
  }

  // When the external value changes (e.g., canceling edits), update the draft.
  useEffect(() => {
    setDraft(value);
  }, [value]);

  // Commit changes when the input loses focus.
  const commit = () => {
    if (draft !== value) {
      onChange(draft);
    }
  };

  const Comp = multiline ? TextArea : Input;

  return (
    <Comp
      autoFocus
      value={draft}
      onChange={e => setDraft(e.target.value)}
      onBlur={commit}
      onPressEnter={!multiline ? commit : undefined}
      placeholder={placeholder}
      autoSize={multiline ? { minRows: 1 } : false}
      style={{
        marginBottom: multiline ? 8 : 4,
        width: '100%',
        border: '1px dashed #d9d9d9',
        padding: '4px 8px'
      }}
    />
  );
};

export default EditableText;
