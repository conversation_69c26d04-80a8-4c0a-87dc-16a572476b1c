.pageWrapper {
    display: flex;
    gap: 40px;
    max-width: 1160px;
    margin: 0 auto;
    font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    background: #fafcff;
    padding: 40px 0 48px 0; /* Đặt padding đ<PERSON><PERSON>, không cần margin-top nếu phía trên có FreeCourseCTA */
    text-align: center;
  }
  
  .leftCol {
    flex: 1 1 60%;
    max-width: 680px;
  }
  
  .rightCol {
    width: 340px;
    flex-shrink: 0;
  }
  
  .pathSection {
    font-size: 14px;
    color: #b0b3b9;
    margin-bottom: 8px;
  }
  
  .levelSection {
    margin-bottom: 10px;
  }
  .levelIntermediate, .levelAdvanced {
    padding: 2px 12px;
    border-radius: 12px;
    font-size: 13px;
    margin-right: 10px;
    font-weight: 500;
  }
  .levelIntermediate {
    background: #e0e6ff;
    color: #626dff;
  }
  .levelAdvanced {
    background: #e7f8e9;
    color: #18b56c;
  }
  
  .title {
    font-size: 2.1rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #272c33;
  }
  
  .ratingSection {
    display: flex;
    align-items: center;
    gap: 7px;
    font-size: 15px;
    margin-bottom: 6px;
  }
  
  .ratingValue { font-weight: 600; }
  .ratingCount { color: #969bb2; }
  .studentCount { margin-left: 10px; color: #74798c; }
  
  .desc {
    margin-bottom: 15px;
    color: #454c5f;
    line-height: 1.5;
  }
  
  .authorSection {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: #74798c;
    margin-bottom: 18px;
  }
  
  
  .avatar {
    width: 32px;
    height: 32px;
    background: #e2e2e2;
    border-radius: 50%;
    display: inline-block;
  }
  
  .updated {
    margin-left: 20px;
    color: #c5c8d4;
  }

  .avatarGroup {
    display: flex;
    align-items: center;
    position: relative;
  }
  
  .avatarCircle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 0 0 2px #fff;
    background: #fff;
    border: 2px solid #fff;
  }
  
  .avatarGroup .avatarCircle:not(:first-child) {
    margin-left: -14px;
    z-index: 2;
    background: #fff;
    border: 2px solid #fff;
  }
  
  
  .tabsSection {
    display: flex;
    gap: 36px;
    border-bottom: 2px solid #eef1f8;
    margin-bottom: 22px;
    font-size: 16px;
  }
  
  .tabsSection span {
    padding-bottom: 10px;
    cursor: pointer;
    color: #a4a7b2;
  }
  
  .tabActive {
    color: #080909;
    font-weight: 600;
    border-bottom: 3px solid #545cff;
  }
  
  .whatLearnSection {
    margin-top: 28px;
    margin-bottom: 24px;
  }
  
  .whatLearnSection h2 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
  }
  
  .learnBox {
    background: #f6fafd;
    border-radius: 14px;
    padding: 28px 28px 18px 28px;
    box-shadow: 0 2px 12px #eef2fa70;
    max-width: 820px;
  }
  
  .learnBox ul {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px 36px;
    list-style: none;
    margin: 0;
    padding: 0;
  }
  
  .learnBox li {
    font-size: 16px;
    color: #253141;
    line-height: 1.65;
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
  }
  
  
  .card {
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 2px 16px 0 #e3e5eb80;
    padding: 22px 26px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .courseImg {
    width: 250px;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 18px;
    background: #f6f7fb;
  }
  
  .priceSection {
    display: flex;
    align-items: center;
    gap: 11px;
    margin-bottom: 10px;
  }
  
  .currentPrice {
    color:var(--secondary-color);
    font-size: 1.7rem;
    font-weight: 700;
  }
  
  .oldPrice {
    text-decoration: line-through;
    color: #babfd0;
    font-size: 1.05rem;
  }
  
  .discount {
    background: #e4fbe4;
    color: #25b35b;
    font-weight: 600;
    font-size: 13px;
    padding: 3px 9px;
    border-radius: 8px;
  }
  
  .enrollBtn {
    width: 100%;
    background: var(--primary-color);
    color: #fff;
    font-weight: 600;
    font-size: 17px;
    padding: 12px 0;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 12px;
    transition: background 0.2s;
  }
  .enrollBtn:hover {
    background: var(--secondary-color);
  }
  
  .guaranteeText {
    color: #8e97af;
    font-size: 13px;
    text-align: center;
    margin-bottom: 17px;
  }
  
  .cardFeatures {
    width: 100%;
  }
  .cardFeatures h4 {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 10px;
  }
  .cardFeatures ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  .cardFeatures li {
    font-size: 15px;
    color: #272c33;
    margin-bottom: 9px;
    display: flex;
    align-items: center;
    gap: 7px;
  }

  /* --- Responsive cho toàn page --- */

/* Tablet */
@media (max-width: 1024px) {
    .pageWrapper, .container {
      max-width: 100vw;
      padding-left: 10px;
      padding-right: 10px;
      gap: 24px !important;
    }
  }
  
  /* Mobile */
  @media (max-width: 768px) {
    .pageWrapper {
      flex-direction: column;
      gap: 0 !important;
      padding: 8px 0 !important;
    }
    .leftCol, .rightCol {
      max-width: 100vw;
      width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
    }
    .rightCol {
      margin-top: 24px !important;
    }
    .heroSection, .whySection, .topicsSection, .testimonialSection,
    .logosSection, .nextStepsSection, .ctaSection {
      padding-left: 10px !important;
      padding-right: 10px !important;
    }
  }
  
  /* Mobile nhỏ */
  @media (max-width: 480px) {
    .sectionTitle {
      font-size: 1.2rem !important;
      text-align: center !important;
    }
    .heroSection h1, .whySection h2, .topicsSection h2 {
      font-size: 1.1rem !important;
      text-align: center !important;
    }
    .leftCol, .rightCol {
      padding: 0 !important;
      margin: 0 !important;
    }
  }
  
  