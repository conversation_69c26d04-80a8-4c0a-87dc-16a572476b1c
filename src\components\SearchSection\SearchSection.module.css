/* SearchSection.module.css */
.searchSection {
  background-color: #FFFFFF;
  padding: 10px 20px 0;
}

.searchContainer {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.searchSubtitle {
  font-size: 14px;
  color: #7A7A7A;
  margin-bottom: 8px;
}

.searchTitle {
  font-size: 36px;
  line-height: 1.2;
  color: var(--primary-color);
  margin: 0 0 32px;
}

.searchForm {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.searchInput {
  flex: 1 1 240px;
  min-width: 200px;
  padding: 12px 16px;
  border: 1px solid #DDD;
  border-radius: 50px;
  outline: none;
  font-size: 14px;
}

.selectWrapper {
  position: relative;
  flex: 0 0 160px;
}

.searchSelect {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border: 1px solid #DDD;
  border-radius: 50px;
  appearance: none;
  font-size: 14px;
  outline: none;
  background-color: #FFF;
  cursor: pointer;
}

.selectArrow {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 12px;
  color: #555;
}

.searchBtn {
  padding: 12px 24px;
  border: none;
  background-color: var(--primary-color);
  color: #FFF;
  border-radius: 50px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

@media (max-width: 600px) {
  .searchForm {
    flex-direction: column;
  }
  .searchInput,
  .selectWrapper,
  .searchBtn {
    width: 100%;
    max-width: 400px;
  }
}
