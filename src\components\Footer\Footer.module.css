/* 📦 Gói toàn bộ */
.footerWrapper {
  position: relative; /* ✅ làm gốc cho absolute bên trong */
  background-color: #323232;
  padding-top: 100px; /* ✅ tạo chỗ cho podcastBox lùi vào */
  overflow: visible; /* ✅ cho phép phần tử bên trong thoát ra ngoài */
  margin-top: 100px;
  height: auto;
  padding-bottom: 80px;
  max-height: 500px;
}

/* 🎧 Box Podcast nổi lên trên Footer */
.podcastBox {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translate(-50%, -50%); /* ✅ đẩy ra ngoài 1/2 chiều cao */
  background-color: #eef3ef;
  width: 90%;
  max-width: 1100px;
  border-radius: 16px;
  padding: 2rem;
  padding-bottom: 160px;
  margin-top: 40px;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  z-index: 999;
  margin: 0;
  height: 200px;
}

.podcastText {
  flex: 1 1 60%;
}

.podcastText h2 {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  color: #111;
}

.podcastText p {
  font-size: 1rem;
  color: #333;
  margin-bottom: 1rem;
}

.listenOn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #111;
  font-weight: bold;
  font-size: 0.95rem;
}

.listenOn i {
  font-size: 1.2rem;
  color: #111;
  cursor: pointer;
}

/* 🔘 Nút VIEW ALL EPISODES */
.podcastButton {
  flex: 1 1 30%;
  text-align: right;
}

.podcastButton button {
  background-color: black;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 999px;
  cursor: pointer;
  font-weight: bold;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.podcastButton button:hover {
  background-color: #333;
}

/* 🦶 Footer chính */
.footer {
  background-color: #323232;
  color: white;
  padding: 2rem 1rem 1.5rem;
  position: relative;
  z-index: 1;
}

.footerTop {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1100px;
  margin: 0 auto;
  padding: 0 1rem;
}

.column {
  flex: 1 1 250px;
  margin: 1rem 0;
}

.column h3 {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.column li {
  margin-bottom: 0.5rem;
}

.column a {
  color: #ccc;
  text-decoration: none;
  transition: color 0.3s;
}

.column a:hover {
  color: #fff;
}

.socials a {
  margin-right: 1rem;
  font-size: 1.2rem;
  color: #fff;
}

/*  Section đăng ký */
.subscribeSection {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;

  max-width: 1100px; /* ✅ cùng chiều rộng với footerTop */
  width: 100%;
  margin: 0 auto 32px; /* ✅ căn giữa */
  padding: 0 1rem; /* ✅ giống hệt footerTop */
  border-top: 1px solid #444;
}

.subscribeSection h2 {
  font-size: 2rem; /* ✅ Tăng kích thước chữ */
  font-weight: 710; /* ✅ Đậm hơn */
  line-height: 1.3; /* ✅ Dễ đọc, thoáng */
  margin-bottom: 1.5rem; /* ✅ Giãn cách dưới */
  letter-spacing: -0.5px; /* ✅ Nhẹ nhàng hơn */

  width: 1000px;
}

.subscribeForm {
  display: flex;
  flex-direction: column;
  max-width: 600px;
  border-bottom: 1px solid #ccc;
}

.subscribeForm input {
  width: 596px;
  flex: 1;
  padding: 0.75rem;
  border: none;
  background: transparent;
  color: white;
}

.subscribeForm button {
  background: #ecff66;
  color: black;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 999px;
  cursor: pointer;
  font-weight: bold;
}

.subscribeForm button:hover {
  background: #d8f24d;
}

/* ⚠️ Bản quyền */
.copyRight {
  margin-top: 2rem;
  font-size: 0.9rem;
  color: #aaa;
  width: 100%;
  text-align: center;
}

/* 📱 Responsive */
@media screen and (max-width: 768px) {
  .podcastBox {
    position: absolute; /* ✅ Nổi lên */
    top: 0;
    left: 50%;
    transform: translateX(-50%) translateY(-50%); /* ✅ Đẩy lên một chút */
    background-color: #eef3ef;
    width: 90%;
    max-width: 1100px;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    z-index: 2;
  }

  .podcastButton {
    text-align: center;
    margin-top: 1rem;
  }

  .footerTop {
    flex-direction: column;
  }

  .subscribeForm {
    flex-direction: column;
    gap: 1rem;
    border: none;
  }

  .subscribeForm input,
  .subscribeForm button {
    width: 100%;
  }
}
/* end of footer*/
