/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  transpilePackages: [
    "@rc-component/util",
    "rc-util",
    "antd",
    "rc-picker",
    "rc-table",
    "rc-tree",
    "rc-select",
    "rc-input",
    "rc-menu",
    "rc-dropdown",
    "rc-tooltip",
    "rc-dialog",
    "rc-drawer",
    "rc-collapse",
    "rc-tabs",
    "rc-pagination",
    "rc-upload",
    "rc-switch",
    "rc-slider",
    "rc-rate",
    "rc-progress",
    "rc-steps",
    "rc-checkbox",
    "rc-field-form",
    "rc-motion",
    "rc-notification",
    "rc-image",
    "rc-mentions",
    "rc-cascader",
    "rc-tree-select",
    "rc-textarea",
    "rc-input-number",
    "rc-segmented",
    "rc-resize-observer",
    "rc-overflow",
    "rc-virtual-list",
  ],
};

export default nextConfig;
