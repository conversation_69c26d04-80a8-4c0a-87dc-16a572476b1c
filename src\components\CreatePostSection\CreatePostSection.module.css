.createPostSection {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px;
  border: 1px solid #e5e7eb;
}

.postInput {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.userAvatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #FF6701 0%, #FF8533 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
  overflow: hidden;
}

.avatarIcon {
  font-size: 24px;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.inputField {
  flex: 1;
  background: #f3f4f6;
  border: 2px solid transparent;
  border-radius: 24px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.inputField:hover {
  border-color: #FF6701;
  background: #fef3f2;
}

.placeholder {
  color: #6b7280;
  font-size: 16px;
  font-weight: 400;
}

.postActions {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.actionBtn {
  flex: 1;
  background: transparent;
  border: none;
  border-radius: 8px;
  padding: 12px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
}

.actionBtn:hover {
  background: #f3f4f6;
}

/* Special styling for job posting button */
.actionBtn:last-child {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.actionBtn:last-child:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b4c8a 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.actionBtn:last-child .actionIcon {
  color: white;
}

.actionIcon {
  font-size: 20px;
  color: #6b7280;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 650px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modalHeader h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

.closeBtn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.closeBtn:hover {
  background: #f3f4f6;
  color: #374151;
}

.modalContent {
  padding: 24px;
}

.textArea {
  width: 100%;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
  transition: border-color 0.3s ease;
}

.textArea:focus {
  outline: none;
  border-color: #FF6701;
  box-shadow: 0 0 0 3px rgba(255, 103, 1, 0.1);
}

.textArea::placeholder {
  color: #9ca3af;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.cancelBtn,
.postBtn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancelBtn {
  background: #f3f4f6;
  color: #374151;
}

.cancelBtn:hover {
  background: #e5e7eb;
}

.postBtn {
  background: linear-gradient(90deg, #FF6701 0%, #FF8533 100%);
  color: white;
}

.postBtn:hover {
  background: linear-gradient(90deg, #e55a01 0%, #e67329 100%);
  transform: translateY(-1px);
}

.postBtn:disabled {
  background-color: #9ca3af !important;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Image Preview Styles */
.imagePreviewSection {
  margin: 20px 0;
}

.imageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 12px;
  margin-bottom: 15px;
}

.imagePreview {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  aspect-ratio: 1;
  background: #f3f4f6;
}

.imagePreview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.removeImageBtn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.removeImageBtn:hover {
  background: rgba(239, 68, 68, 0.9);
  transform: scale(1.1);
}

/* Media Options */
.mediaOptions {
  display: flex;
  gap: 12px;
  margin: 20px 0;
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  border: 2px dashed #d1d5db;
}

.mediaBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #374151;
}

.mediaBtn:hover {
  border-color: #FF6701;
  color: #FF6701;
  background: #fef3f2;
}

.mediaBtn svg {
  font-size: 18px;
}

/* User Info Section in Modal */
.userInfoSection {
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modalUserAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: linear-gradient(135deg, #FF6701 0%, #FF8533 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.modalAvatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modalAvatarIcon {
  font-size: 20px;
}

.userDetails {
  flex: 1;
}

.userName {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 2px;
}

.privacySelector {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #f3f4f6;
  border-radius: 6px;
  font-size: 13px;
  color: #6b7280;
  cursor: pointer;
  width: fit-content;
  transition: background 0.2s ease;
}

.privacySelector:hover {
  background: #e5e7eb;
}

.privacyIcon {
  font-size: 14px;
}

.dropdownIcon {
  font-size: 12px;
}

/* Loading animation */
.spinIcon {
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .createPostSection {
    margin: 0 16px 30px 16px;
    padding: 16px;
  }
  
  .postActions {
    flex-direction: column;
    gap: 8px;
  }
  
  .actionBtn {
    width: 100%;
  }
  
  .modal {
    margin: 20px;
    width: calc(100% - 40px);
    max-height: calc(100vh - 40px);
  }
  
  .modalContent {
    padding: 16px;
  }
  
  .modalActions {
    flex-direction: column-reverse;
    gap: 8px;
  }
  
  .cancelBtn,
  .postBtn {
    width: 100%;
    padding: 12px;
  }
  
  .imageGrid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
  }
  
  .mediaOptions {
    flex-direction: column;
    gap: 8px;
  }
  
  .mediaBtn {
    width: 100%;
    justify-content: center;
  }
}
