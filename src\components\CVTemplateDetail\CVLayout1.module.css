/* Container chính */
.container {
  width: 100%;
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
  padding-top: 120px;
  margin: none;
}


.mainLayout {
  display: flex;
  gap: 32px;
  max-width: 1000px;
  margin: 0 auto;
  align-items: flex-start;
  width: 1100px;
}

/* CV Card */
.cvCard {
  flex: 1;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(44,62,80,0.09);
  padding: 28px 32px 24px 32px;
  min-height: 600px;
  position: relative;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.4;
  color: #333;
  max-width: 780px;
}
.cvCard.pdf-export-mode{ width:720px!important; }
.cvCard.pdf-export-mode .gridTwoCol{
  grid-template-columns:250px calc(720px - 250px - 24px)!important;
}
.cvCard.pdf-export-mode{ font-size:14px!important; }



.avatar {
  width: 145px;
  height: 145px;
  object-fit: cover;
  border-radius: 50%;
  border: 5px solid #e6e6e6;
  flex-shrink: 0;
  margin: auto;
  transition: all 0.3s ease;
}

.avatar:hover {
  transform: scale(1.02);
}

/* Cải thiện hiển thị nút upload */
.avatarContainer {
  position: relative;
  display: inline-block;
}

.avatarUpload {
  position: absolute;
  bottom: 5px;
  right: 5px;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10; /* Đảm bảo nút luôn hiển thị trên cùng */
  opacity: 1 !important; /* Luôn hiển thị */
  display: flex !important;
  align-items: center;
  justify-content: center;
}

/* Upload ảnh hover effect - đảm bảo luôn hiển thị */
.upload-overlay {
  opacity: 0.8 !important;
  transition: opacity 0.3s ease;
}

.avatar:hover + .upload-overlay,
.upload-overlay:hover {
  opacity: 1 !important;
}

.fullname {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #212f3f;
}

.position {
  color: #00b14f;
  font-weight: 500;
  font-size: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.summary {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
  max-width: 800px;
}

/* Grid 2 cột trong CV */
.gridTwoCol {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 32px;
}

/* Các block trong CV */
.cvBlock {
  margin-bottom: 24px;
}

.blockTitle {
  font-size: 15px;
  font-weight: 700;
  color: #212f3f;
  border-bottom: 2px solid #00b14f;
  margin-bottom: 12px;
  padding-bottom: 4px;
  text-transform: uppercase;
}

.blockTime {
  color: #2d9c46;
  font-weight: 600;
  font-size: 13px;
  margin-left: 8px;
}

.boldText {
  font-weight: 600;
  color: #212f3f;
  margin-top: 4px;
}

.blockList {
  list-style: none;
  padding-left: 0;
  margin: 8px 0 0 0;
}

.blockList li {
  margin-bottom: 4px;
  padding-left: 16px;
  position: relative;
  font-size: 14px;
  line-height: 1.4;
}

.blockList li:before {
  content: "•";
  color: #00b14f;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* Sidebar bên trái của CV */
.sideBlock {
  margin-bottom: 25px;
  padding: 5px;
  border-radius: 8px;
  color: #666;
}

.sideBlockTitle {
  font-size: 13px;
  font-weight: 700;
  color: #212f3f;
  margin-bottom: 12px;
  text-transform: uppercase;
  border-bottom: 1px solid #ddd;
  padding-bottom: 4px;
}

.sideBlock div {
  margin-bottom: 6px;
  font-size: 13px;
  line-height: 1.4;
  
}



.btnBack {
  margin-top: 4px;
}

/* Responsive */
@media (max-width: 500px) {
  .mainLayout {
    flex-direction: column;
    gap: 20px;
  }

  .rightCol {
    width: 100%;
  }

  .gridTwoCol {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .profileHeader {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .editModeToggle {
    position: fixed;
    top: 80px;
    right: 10px;
  }
}

/* Cột phải - Create Box */
.rightCol {
  width: 320px;
  flex-shrink: 0;
}

.createBox {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(44,62,80,0.07);
  padding: 24px 20px;
  font-size: 15px;
}

.createBoxTitle {
  font-weight: 600;
  font-size: 17px;
  margin-bottom: 18px;
  color: #212f3f;
}

.radioGroup {
  margin-bottom: 20px;
}

.radioGroup .ant-radio-wrapper {
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.4;
}

/* Edit Mode Styles */
.editableField {
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;
}

.editableField:hover {
  background-color: #f0f0f0;
  cursor: text;
}

.editMode .editableField {
  background-color: #fff;
  border: 1px solid #d9d9d9;
  padding: 4px 8px;
}

.editButton {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.sideBlock:hover .editButton,
.cvBlock:hover .editButton {
  opacity: 1;
}

.editModeToggle {
  position: fixed;
  top: 100px;
  right: 20px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.avatarContainer {
  position: relative;
  display: inline-block;
}

.avatarUpload {
  position: absolute;
  bottom: 0;
  right: 0;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.arrayItemContainer {
  position: relative;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.arrayItemContainer:hover {
  background-color: #f9f9f9;
}

.deleteButton {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.arrayItemContainer:hover .deleteButton {
  opacity: 1;
}

.addButton {
  border: 1px dashed #d9d9d9;
  background: #fafafa;
  color: #666;
  transition: all 0.2s ease;
}

.addButton:hover {
  border-color: #40a9ff;
  color: #40a9ff;
  background: #f6ffed;
}

/* Upload ảnh hover effect */
.avatar:hover + .upload-overlay,
.upload-overlay:hover {
  opacity: 1 !important;
}

.avatar {
  transition: all 0.3s ease;
}

.avatar:hover {
  transform: scale(1.00);
}

/* Tối ưu cho PDF export */
.cvCard.pdf-export-mode {
  background: white !important;
  color: #333 !important;
  font-family: 'Arial', 'Helvetica', sans-serif !important;
  box-shadow: none !important;
  border: none !important;
  margin: 0 !important;
  padding: 20px !important;
  max-width: none !important;
  min-height: auto !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  overflow: visible !important;
  width: 760px !important;
  max-width: 760px !important;
  min-width: 760px !important;
  font-size: 16.5px !important;
  /* Không giới hạn chiều cao để nội dung có thể kéo dài */
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
}

/* Đảm bảo tất cả các phần tử trong PDF có word-wrap */
.cvCard.pdf-export-mode * {
  box-sizing: border-box !important;
  -webkit-print-color-adjust: exact !important;
  print-color-adjust: exact !important;
  color: inherit !important;
  visibility: visible !important;
  opacity: 1 !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: break-word !important;
  white-space: normal !important;
}

/* Đảm bảo các mô tả dài tự động xuống dòng */
.cvCard.pdf-export-mode p,
.cvCard.pdf-export-mode span,
.cvCard.pdf-export-mode div,
.cvCard.pdf-export-mode li {
  max-width: 100% !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: break-word !important;
  white-space: normal !important;
  line-height: 1.4 !important;
}

/* Đảm bảo layout grid hiển thị đúng */
.cvCard.pdf-export-mode .gridTwoCol{
  grid-template-columns: 250px calc(760px - 250px - 24px) !important;
  gap: 24px !important;
  display: grid !important; /* Đảm bảo grid layout được duy trì */
}

/* Đảm bảo ảnh hiển thị */
.cvCard.pdf-export-mode img {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Cải thiện kích thước cho PDF export */
.cvCard.pdf-export-mode {
  min-width: 800px !important;
  max-width: 800px !important;
  width: 800px !important;
}

.cvCard.pdf-export-mode .gridTwoCol {
  grid-template-columns: 250px 1fr !important;
  gap: 20px !important;
}

/*CSS cho CVLayout1*/
.container {
  width: 100%;
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
  padding-top: 120px;
}

.mainLayout {
  display: flex;
  gap: 32px;
  max-width: 1000px;
  margin: 0 auto;
  align-items: flex-start;
  width: 1100px;
}

.cvCard {
  flex: 1;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 28px 32px;
  font-family: 'Segoe UI', Tahoma, sans-serif;
  color: #333;
}

.avatar {
  width: 140px;
  height: 140px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #ddd;
}

.gridTwoCol {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 24px;
}

.sideBlock {
  flex: 1;
}

.sideBlock div {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 6px;
}

.sideBlockTitle {
  font-weight: 700;
  margin-bottom: 8px;
  font-size: 15px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 4px;
}

.fullname {
  font-size: 20px;
  font-weight: 700;
}

.position {
  font-size: 15px;
  font-weight: 500;
  margin-top: 4px;
}

.summary {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.cvBlock {
  margin-bottom: 20px;
}

.blockTitle {
  font-size: 15px;
  font-weight: 700;
  border-bottom: 2px solid #333;
  margin-bottom: 12px;
  padding-bottom: 4px;
  text-transform: uppercase;
}

.blockTime {
  font-size: 13px;
  font-weight: 600;
  color: #555;
  margin-left: 6px;
}

.boldText {
  font-weight: 600;
}

.blockList {
  list-style: disc inside;
  margin-top: 8px;
}

.rightCol {
  width: 320px;
}

.createBox {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
}

.createBoxTitle {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 16px;
}

.btnBack {
  margin-top: 8px;
}

/* PDF Export tối ưu */
.cvCard.pdf-export-mode {
  width: 800px !important;
}
