.team {
  max-width: 1200px;
  margin: 0 auto;
  padding: 18px 20px;
  font-family: "Roboto", sans-serif;
  color: #1a1a1a;
}

.header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 48px;
}

.overline {
  display: inline-block;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  color: #fba931;
  margin-bottom: 8px;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
}

.descWrap {
  max-width: 400px;
  text-align: right;
}

.description {
  margin: 0 0 16px;
  font-size: 1rem;
  line-height: 1.6;
}

.cta {
  padding: 10px 24px;
  font-size: 0.9rem;
  font-weight: 600;
  background-color: #fba931;
  border: none;
  border-radius: 24px;
  cursor: pointer;
  transition: background 0.3s;
}

.cta:hover {
  background-color: #e38b1f;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 32px;
}

.card {
  text-align: center;
}

.avatarWrap {
  width: 100%;
  padding-top: 100%;
  position: relative;
  background: #f5f5f5;
  clip-path: polygon(
    12% 0%,
    88% 0%,
    100% 12%,
    100% 88%,
    88% 100%,
    12% 100%,
    0% 88%,
    0% 12%
  );
  margin-bottom: 16px;
  overflow: hidden;
}

.avatar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.name {
  font-size: 1.125rem;
  font-weight: 700;
  margin: 0 0 4px;
}

.role {
  font-size: 0.875rem;
  color: #555;
  margin: 0 0 12px;
}

.socials {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.socialLink {
  font-size: 1.125rem;
  color: #333;
  transition: color 0.2s;
}

.socialLink:hover {
  color: #fba931;
}

/* Responsive */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
  }
  .descWrap {
    text-align: left;
  }
}
