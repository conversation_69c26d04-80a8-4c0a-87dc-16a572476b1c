/* Register.module.css */

.registerWrapper {
    display: flex;
    height: 100vh;
    background-color: #f2f2f2;
    font-family: 'Inter', sans-serif;
    padding: 2rem;
    gap: 0;
  }
  
  .registerLeft {
    border-radius: 70%;
    flex: 1;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2rem 3rem;
    border-radius: 1rem;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  }
  
  .registerLeft h2{
    text-align: center;
    margin-bottom: 2rem;
  }

  .brand h2 {
    text-align: left;
    color: #000;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }
  
  .registerForm {
    display: flex;
    flex-direction: column;
  }
  
  .registerForm label {
    font-size: 0.85rem;
    margin-bottom: 0.3rem;
    color: #333;
  }
  
  .registerForm input {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid #ddd;
    background-color: white;
    margin-bottom: 1rem;
    font-size: 0.9rem;
  }

  .passwordWrapper {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .passwordInput {
    width: 100%;
    padding-right: 2.5rem; /* chừa chỗ cho icon */
  }
  
  .eyeIcon {
    position: absolute;
    right: 12px;
    top: 40%;
    transform: translateY(-50%);
    color: #141313;
    cursor: pointer;
    font-size: 1rem;
  }
  
  
  .registerOptions {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #333;
    line-height: 1.5;
    margin-bottom: 1rem;
    flex-wrap: wrap; /* cho responsive tốt */
  }
  
  .registerOptions input[type="checkbox"] {
    margin-top: 3px;
    accent-color: #007bff;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
  
  /* Link trong Next.js cần chỉ định tag con */
  .registerOptions :global(a) {
    color: black;
    text-decoration: none;
    font-weight: bold;
  }
  
  .registerOptions :global(a:hover) {
    text-decoration: underline;
  }
  
  
  .registerButton {
    background-color: #000;
    color: #fff;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease; /* 👈 hiệu ứng mượt */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  .registerButton:hover {
    background-color: #1a1a1a;
    transform: scale(1.02);           /* phóng nhẹ */
    box-shadow: 0 6px 14px rgba(0, 0, 0, 0.3);
  }
  
  .registerBottom {
    margin-top: 1.5rem;
    font-size: 0.85rem;
    text-align: center;
    color: #000;
  }
  
  .registerBottom a {
    color: #000;
    font-weight: 500;
    text-decoration: none;
  }
  
  .registerSocials {
    margin-top: 1.2rem;
    display: flex;
    justify-content: center;
    gap: 1rem;
    font-size: 1.4rem;
    color: #555;
  }

  .socialIcon {
    width: 48px;
    height: 48px;
    background: linear-gradient(145deg, #ffffff, #e6e6e6); /* ánh sáng nhẹ */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  
  .socialIcon::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 0.3s;
  }
  
  .socialIcon:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
  
  .socialIcon:hover::after {
    opacity: 1;
  }
  
  .registerRight {
    flex: 1;
    background: #0a0a0a;
    color: #fff;
    padding: 3rem;
    border-radius: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url('/bg-pattern.png'); /* optional background */
    background-size: cover;
    background-position: center;
  }
  
  .registerPanel {
    max-width: 400px;
    text-align: left;
  }
  
  .registerPanel h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }
  
  .registerPanel p {
    font-size: 0.85rem;
    line-height: 1.5;
    margin-bottom: 1rem;
  }
  
  .registerCta {
    position: relative;
    background-color: #3b3b3b;
    color: #fff;
    padding: 2rem;
    max-width: 480px;
    border-radius: 24px;
    font-family: 'Inter', sans-serif;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }
  
  

  .registerCta h5 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  
  .registerCta p {
    font-size: 0.85rem;
    margin-bottom: 1rem;
    flex: 1;
  }
  
  .ctaRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap; /* Hữu ích trên mobile */
  }
  
  .ctaRow p {
    margin: 0;
    color: #ccc;
    font-size: 0.85rem;
    line-height: 1.5;
    flex: 1; /* chiếm hết phần còn lại bên trái */
  }
  
/* Avatars */
.avatars {
  display: flex;
  align-items: center;
  justify-content: flex-end; /* ✅ Đẩy toàn bộ avatar sang phải */
  gap: 0; /* Không khoảng cách thừa giữa ảnh */
}


.avatars img {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  border: 2px solid white;
  object-fit: cover;
  margin-left: -10px;
  background-color: #151414;
  z-index: 1;
}

.avatars span {
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: -10px;
  background: rgb(27, 27, 27);
  border-radius: 50%;
  font-size: 0.8rem;
  font-weight: bold;
  color: #eee5e5;
  border: 2px solid #fff;
  z-index: 0;
}


/* reponsive*/
  @media (max-width: 768px) {
    .registerWrapper {
      flex-direction: column;     /* Stack dọc thay vì chia 2 cột */
      padding: 1rem;
      height: auto;
    }
  
    .registerLeft,
    .registerRight {
      flex: none;
      width: 100%;
      border-radius: 1rem;
      padding: 2rem 1.5rem;
    }
  
    .registerRight {
      display: none; /* Hoặc giữ lại nhưng thu gọn */
    }
  
    .registerForm input {
      font-size: 1rem;
    }
  
    .registerButton {
      font-size: 1rem;
    }
  
    .registerSocials {
      gap: 0.8rem;
    }
  
    .socialIcon {
      width: 44px;
      height: 44px;
    }
  }

  /* Message styles */
.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
}

.message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Error input styles */
.inputError {
  border-color: #e74c3c !important;
  background-color: #fdf2f2 !important;
}

.errorText {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
  margin-bottom: 8px;
  display: block;
}

/* Loading button */
.registerButton.loading {
  opacity: 0.7;
  cursor: not-allowed;
  background-color: #ccc;
}

.registerButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loginLink {
  color: var(--primary-color);
  text-decoration: none;
}

.loginLink:hover {
  color: var(--primary-color);
}

/* Email verification instructions styles */
.verificationInstructions {
  text-align: center;
  padding: 2rem 0;
}

.emailIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.verificationInstructions h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.verificationInstructions p {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.instructionSteps {
  margin: 2rem 0;
  text-align: left;
}

.step {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
}

.stepNumber {
  background-color: var(--primary-color);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 0.85rem;
  font-weight: bold;
}

.verificationActions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.signInButton {
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.3s ease;
  text-align: center;
}

.signInButton:hover {
  background-color: var(--primary-color);
}

/* Disabled sign in button */
.signInButton.disabled {
  background-color: #ccc;
  color: #666;
  cursor: not-allowed;
  pointer-events: none;
}

.signInButton.disabled:hover {
  background-color: #ccc;
  transform: none;
}

/* Check verification button */
.checkVerificationButton {
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
  font-size: 0.9rem;
}

.checkVerificationButton:hover {
  background-color: var(--hover-color);
}

.checkVerificationButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Email verification status indicator */
.verificationStatus {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  margin: 1rem 0;
  font-size: 0.9rem;
  text-align: center;
}

.verificationStatus.pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.verificationStatus.verified {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

/* Login prompt spacing */
.loginPrompt {
  margin-top: 2rem !important;
  text-align: center;
  font-size: 0.9rem;
  color: #666;
}

.loginPrompt .loginLink {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.loginPrompt .loginLink:hover {
  color: var(--last-color);
}

/* Register Again Button - Modern Design */
.registerAgainButton {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  border: 2px solid #dee2e6;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  text-transform: none;
  letter-spacing: 0.025em;
}

.registerAgainButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.registerAgainButton:hover {
  background: linear-gradient(135deg, var(--last-color) 0%, var(--hover-color) 100%);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.15);
}

.registerAgainButton:hover::before {
  left: 100%;
}

.registerAgainButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.1s ease;
}

.registerAgainButton:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

