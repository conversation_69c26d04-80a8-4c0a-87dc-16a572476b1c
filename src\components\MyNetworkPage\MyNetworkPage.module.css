.myNetworkPage {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.header h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.searchContainer {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.searchIcon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 18px;
}

.searchInput {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.searchInput:focus {
  border-color: #3b82f6;
}

.searchLoadingIcon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 16px;
  animation: spin 1s linear infinite;
}

.tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 24px;
  border-bottom: 2px solid #f3f4f6;
  overflow-x: auto;
}

.tab {
  padding: 12px 16px;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.tab:hover {
  color: #374151;
  background: #f9fafb;
}

.tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.content {
  margin-top: 24px;
}

.section {
  margin-bottom: 32px;
}

.section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.userGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.userCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.userCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.userAvatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  overflow: hidden;
}

.userAvatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatarIcon {
  font-size: 32px;
  color: white;
}

.userInfo {
  flex: 1;
  margin-bottom: 16px;
}

.userName {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.userTitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 4px 0;
}

.userLocation {
  font-size: 13px;
  color: #9ca3af;
  margin: 0;
}

.userActions {
  display: flex;
  gap: 8px;
  width: 100%;
}

.connectBtn,
.acceptBtn,
.rejectBtn,
.cancelBtn,
.removeFriendBtn {
  flex: 1;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.connectBtn {
  background: #3b82f6;
  color: white;
}

.connectBtn:hover {
  background: #2563eb;
}

.acceptBtn {
  background: #10b981;
  color: white;
}

.acceptBtn:hover {
  background: #059669;
}

.rejectBtn {
  background: #ef4444;
  color: white;
}

.rejectBtn:hover {
  background: #dc2626;
}

.cancelBtn {
  background: #f59e0b;
  color: white;
}

.cancelBtn:hover {
  background: #d97706;
}

.removeFriendBtn {
  background: #dc2626;
  color: white;
}

.removeFriendBtn:hover {
  background: #b91c1c;
  transform: translateY(-1px);
}

.actionIcon {
  font-size: 16px;
}

.pendingText {
  color: #6b7280;
  font-size: 14px;
  padding: 8px;
  text-align: center;
  width: 100%;
}

.emptyState {
  text-align: center;
  color: #6b7280;
  font-size: 16px;
  padding: 40px 20px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #d1d5db;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loadingIcon {
  font-size: 32px;
  margin-bottom: 12px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .myNetworkPage {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .searchContainer {
    max-width: none;
  }
  
  .userGrid {
    grid-template-columns: 1fr;
  }
  
  .tabs {
    flex-wrap: wrap;
  }
  
  .userActions {
    flex-direction: column;
  }
  
  .connectBtn,
  .acceptBtn,
  .rejectBtn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 24px;
  }
  
  .userCard {
    padding: 16px;
  }
  
  .userAvatar {
    width: 60px;
    height: 60px;
  }
  
  .avatarIcon {
    font-size: 24px;
  }
}
