.signinWrapper {
  display: flex;
  height: 100vh;
  background-color: #f2f2f2;
  font-family: 'Inter', sans-serif;
  padding: 2rem;
  gap: 0;
}

.signinLeft {
  border-radius: 70%;
  flex: 1;
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 2rem 3rem;
  border-radius: 1rem;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.signinLeft h2{
  text-align: center;
  margin-bottom: 2rem;
}

.brand h2 {
  text-align: left;
  color: #000;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.signinForm {
  display: flex;
  flex-direction: column;
}

.signinForm label {
  font-size: 0.85rem;
  margin-bottom: 0.3rem;
  color: #333;
}

.signinForm input {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid #ddd;
  background-color: white;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.passwordWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.passwordInput {
  width: 100%;
  padding-right: 2.5rem; /* chừa chỗ cho icon */
}

.eyeIcon {
  position: absolute;
  right: 12px;
  top: 40%;
  transform: translateY(-50%);
  color: #141313;
  cursor: pointer;
  font-size: 1rem;
}


.signinOptions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
  margin-bottom: 1rem;
}

.signinOptions input[type="checkbox"] {
  accent-color: white; /* ✅ Đặt màu trắng cho checkbox (hỗ trợ tốt trên trình duyệt hiện đại) */
  width: 16px;
  height: 12px;
  cursor: pointer;
}

.signinButton {
  background-color: #000;
  color: #fff;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease; /* 👈 hiệu ứng mượt */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.signinButton:hover {
  background-color: #1a1a1a;
  transform: scale(1.02);           /* phóng nhẹ */
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.3);
}

.signinBottom {
  margin-top: 1.5rem;
  font-size: 0.85rem;
  text-align: center;
  color: #000;
}

.signinBottom a {
  color: #000;
  font-weight: 500;
  text-decoration: none;
}

.signinSocials {
  margin-top: 1.2rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
  font-size: 1.4rem;
  color: #555;
}

.socialIcon {
  width: 48px;
  height: 48px;
  background: white;
  border-radius: 50%;                  /* Bo tròn icon */
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
}

.socialIcon:hover {
  transform: scale(1.1);               /* Hiệu ứng phóng to nhẹ khi hover */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
.signinRight {
  flex: 1;
  background: #0a0a0a;
  color: #fff;
  padding: 3rem;
  border-radius: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('/bg-pattern.png'); /* optional background */
  background-size: cover;
  background-position: center;
}

.signinPanel {
  max-width: 400px;
  text-align: left;
}

.signinPanel h4 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.signinPanel p {
  font-size: 0.85rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.signinCta {
  position: relative;
  background-color: #3b3b3b;
  color: #fff;
  padding: 2rem;
  max-width: 480px;
  border-radius: 24px;
  font-family: 'Inter', sans-serif;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.signinCta h5 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.signinCta p {
  font-size: 0.85rem;
  margin-bottom: 1rem;
  flex: 1;
}

.ctaRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap; /* Hữu ích trên mobile */
}

.ctaRow p {
  margin: 0;
  color: #ccc;
  font-size: 0.85rem;
  line-height: 1.5;
  flex: 1; /* chiếm hết phần còn lại bên trái */
}

/* Avatars */
.avatars {
display: flex;
align-items: center;
justify-content: flex-end; /* ✅ Đẩy toàn bộ avatar sang phải */
gap: 0; /* Không khoảng cách thừa giữa ảnh */
}


.avatars img {
width: 25px;
height: 25px;
border-radius: 50%;
border: 2px solid white;
object-fit: cover;
margin-left: -10px;
background-color: #151414;
z-index: 1;
}

.avatars span {
width: 25px;
height: 25px;
display: flex;
align-items: center;
justify-content: center;
margin-left: -10px;
background: rgb(27, 27, 27);
border-radius: 50%;
font-size: 0.8rem;
font-weight: bold;
color: #eee5e5;
border: 2px solid #fff;
z-index: 0;
}

/* Error message styling */
.errorMessage {
  background-color: #fff5f5;
  color: #e53e3e;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  border: 1px solid #feb2b2;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(229, 62, 62, 0.1);
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* reponsive*/
@media (max-width: 768px) {
  .signinWrapper {
    flex-direction: column;     /* Stack dọc thay vì chia 2 cột */
    padding: 1rem;
    height: auto;
  }

  .signinLeft,
  .signinRight {
    flex: none;
    width: 100%;
    border-radius: 1rem;
    padding: 2rem 1.5rem;
  }

  .signinRight {
    display: none; /* Hoặc giữ lại nhưng thu gọn */
  }

  .signinForm input {
    font-size: 1rem;
  }

  .signinButton {
    font-size: 1rem;
  }

  .signinSocials {
    gap: 0.8rem;
  }

  .socialIcon {
    width: 44px;
    height: 44px;
  }
}
