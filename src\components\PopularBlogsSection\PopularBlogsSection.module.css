.section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 18px 20px;
}

.heading {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 32px;
  color: #1a1a1a;
  text-align: left;
}

.status {
  text-align: center;
  font-size: 1rem;
  color: #555;
  padding: 20px;
}
.error {
  color: red;
}

/* Grid responsive: 3 cột desktop, 2 cột tablet, 1 cột mobile */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 24px;
}

.card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  will-change: transform, box-shadow;
}
.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Wrapper ảnh giữ aspect ratio 16:9 hoặc 4:3 tùy chỉnh */
.imageWrapper {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 ratio */
  display: block;
  background: #f0f0f0;
}
.thumbnail {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.5s ease;
}

.imageWrapper:hover .thumbnail {
  transform: scale(1.05);
}

.info {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 8px;
  line-height: 1.3;
  /* Giới hạn 2 dòng, ẩn overflow */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.titleLink {
  color: #1a1a1a;
  text-decoration: none;
  transition: color 0.2s;
}
.titleLink:hover {
  color: #e74c3c;
}

.metaRow {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #777;
  margin-bottom: 12px;
}
.date {
  /* có thể custom định dạng */
}
.readTime {
}

.readMore {
  margin-top: auto;
  font-size: 0.875rem;
  color: #e74c3c;
  text-decoration: none;
  border: 1px solid #e74c3c;
  border-radius: 4px;
  padding: 6px 12px;
  align-self: flex-start;
  transition: background 0.2s, color 0.2s;
}
.readMore:hover {
  background: #e74c3c;
  color: #fff;
}
