.heroSection {
  position: relative;
  width: 100%;
  /* Gradient background based on the image_ac49b2.png */
  background: linear-gradient(135deg, #f87979 0%, #ffc071 50%, #7b8bff 100%);
  overflow: hidden; /* Ensures content stays within bounds */
  color: white; /* Default text color for the hero section */
  display: flex;
  flex-direction: column; /* Stacks children vertically */
  justify-content: center; /* Centers content vertically */
  align-items: center; /* Centers content horizontally */
  text-align: center; /* Ensures text inside is centered */
  height: 70vh; /* Responsive height, takes 70% of viewport height */
  min-height: 500px; /* Ensures a minimum height on smaller screens */
  padding: 180px 16px 60px; /* Padding for top, sides, and bottom */
  margin-top: 5px; /* Giữ margin-top này nếu bạn muốn đẩy hero section xuống dưới nav/header */
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1); /* Subtle overlay for better text contrast */
  pointer-events: none; /* Allows interaction with elements below */
  z-index: 0; /* Ensures overlay is behind the content */
}

.content {
  position: relative;
  max-width: 900px; /* Increased max-width to accommodate the title and search bar */
  width: 100%;
  z-index: 1; /* Ensures content is above the overlay */
  padding: 0 20px; /* Padding for content to prevent it from touching screen edges */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.title {
  font-size: 3.5rem; /* Larger font size as seen in the image */
  line-height: 1.2;
  margin-bottom: 40px; /* Increased margin for better spacing */
  font-weight: 600; /* Medium-bold font weight */
  color: white; /* Pure white for the title as in the image */
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.25); /* More pronounced shadow */
}

.highlight {
  color: inherit;
}

.searchForm {
  margin-bottom: 60px; /* More space between search bar and features */
  width: 100%; /* Ensures the form takes full width of .content */
}

.searchWrapper {
  display: flex;
  max-width: 650px; /* Adjusted max-width to match image proportions */
  width: 100%;
  margin: 0 auto; /* Centers the search bar */
  background: white; /* Solid white background as in the image */
  border-radius: 12px; /* Slightly rounded corners, not fully pill-shaped */
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2); /* Stronger shadow */
}

.searchInput {
  flex: 1; /* Allows input to grow and take available space */
  border: none;
  background: transparent; /* Transparent background to show .searchWrapper's white background */
  padding: 18px 25px; /* Larger padding for a more spacious input field */
  font-size: 1.15rem; /* Larger font size for input */
  outline: none; /* Removes outline on focus */
  color: #333; /* Darker text color for input */
}

.searchInput::placeholder {
  color: #888; /* Softer placeholder color */
}

.searchButton {
  border: none;
  background-color: var(
    --primary-color
  ); /* Darker button color as in the image */
  color: #fff;
  padding: 18px 35px; /* Larger padding for the button */
  font-size: 1.15rem; /* Matches input font size */
  cursor: pointer;
  border-radius: 0 12px 12px 0; /* Rounded only on the right side, matching .searchWrapper */
  transition: background-color 0.3s ease-in-out;
  font-weight: 600; /* Make button text bold */
}

.searchButton:hover {
  background-color: var(--secondary-color); /* Slightly lighter on hover */
}

/* --- Features Section --- */
.features {
  display: flex;
  justify-content: space-around; /* Distributes space evenly around items */
  align-items: flex-start; /* Aligns items to the top vertically */
  gap: 20px; /* Spacing between feature items */
  flex-wrap: wrap; /* Allows items to wrap to the next line */
  max-width: 900px; /* Limits overall width of the features section */
  width: 100%;
  padding: 0 10px; /* Horizontal padding */
  box-sizing: border-box; /* Ensures padding does not increase total width */
}

.featureItem {
  display: flex; /* Arranges icon and text horizontally */
  align-items: flex-start; /* Aligns icon and text to the top */
  text-align: left; /* Aligns text content to the left */
  flex-grow: 1; /* Allows item to grow */
  flex-shrink: 1; /* Allows item to shrink */
  /* Calculating flex-basis for 3 items on one row, considering gap */
  /* (max-width of .features - (gap * 2)) / 3 = (900 - 40) / 3 = 286.66px */
  flex-basis: 280px; /* Base value, allows for growth/shrinkage */
  max-width: calc(
    33.33% - 14px
  ); /* Approximately 1/3 width minus gap for fitting */
  margin-bottom: 0; /* No bottom margin needed when on a single row */
}

.iconCircle {
  width: 40px; /* Size of the icon circle */
  height: 40px;
  border-radius: 50%; /* Creates a circle shape */
  background: rgba(
    255,
    255,
    255,
    0.2
  ); /* Background color of the circle (similar to image) */
  display: flex;
  justify-content: center; /* Centers icon horizontally */
  align-items: center; /* Centers icon vertically */
  margin-right: 15px; /* Space between icon and text */
  flex-shrink: 0; /* Prevents icon circle from shrinking */
}

.featureIcon {
  font-size: 1.5rem; /* Icon size */
  color: white; /* Icon color */
}

.featureText {
  /* Container for title and description */
  flex-grow: 1; /* Allows text part to grow */
}

.featureTitle {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 5px 0; /* Space between title and description */
  color: white;
  text-align: left; /* Ensures title is left-aligned */
  /* white-space: nowrap; */ /* Removed nowrap to allow title to wrap on multiple lines if long */
  /* overflow: hidden; */ /* Removed overflow/text-overflow to allow full text to show */
  /* text-overflow: ellipsis; */
  line-height: 1.3; /* Adjusted line-height for better readability */
}

.featureDesc {
  font-size: 0.9rem;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  text-align: left; /* Ensures description is left-aligned */
}

/* --- Responsive Adjustments --- */

/* Tablet (max-width: 1024px) */
@media (max-width: 1024px) {
  .heroSection {
    height: 60vh;
    min-height: 450px;
    padding: 60px 16px 40px;
  }
  .title {
    font-size: 2.8rem;
    margin-bottom: 30px;
  }
  .searchWrapper {
    max-width: 600px;
  }
  .searchInput,
  .searchButton {
    padding: 15px 20px;
    font-size: 1.05rem;
  }
  .features {
    gap: 20px; /* Reduced gap on tablet */
    max-width: 600px; /* Adjusted max-width for features */
    justify-content: space-around; /* Changed to space-around for better centering of 2 items */
  }
  .featureItem {
    flex-basis: calc(50% - 15px); /* 2 items per row, adjusted for gap */
    max-width: calc(50% - 15px);
    margin-bottom: 20px; /* Add margin-bottom when wrapping */
  }
  .iconCircle {
    width: 35px;
    height: 35px;
  }
  .featureIcon {
    font-size: 1.3rem;
  }
  .featureTitle {
    font-size: 1.1rem;
  }
  .featureDesc {
    font-size: 0.85rem;
  }
}

/* Mobile (max-width: 768px) */
@media (max-width: 768px) {
  .heroSection {
    height: auto; /* Allow height to adjust to content */
    min-height: 500px; /* Ensure a good minimum height */
    padding: 50px 16px 30px;
  }
  .title {
    font-size: 2.2rem;
    margin-bottom: 25px;
  }
  .searchForm {
    margin-bottom: 40px;
  }
  .searchWrapper {
    flex-direction: column; /* Input and button stack vertically */
    border-radius: 12px; /* Uniform border-radius */
    max-width: 90%; /* Occupy more width */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15); /* Slightly less shadow */
  }
  .searchInput {
    width: 100%;
    border-radius: 12px 12px 0 0; /* Top rounded corners */
    padding: 15px 20px;
    font-size: 1rem;
  }
  .searchButton {
    width: 100%;
    border-radius: 0 0 12px 12px; /* Bottom rounded corners */
    padding: 15px 20px;
    font-size: 1rem;
    margin-top: 1px; /* Small gap between input and button */
  }
  .features {
    flex-direction: column; /* Feature items stack vertically */
    gap: 15px; /* Reduced gap when stacked */
    padding: 0 16px;
    align-items: flex-start; /* Aligns items to the left when stacked */
    max-width: 400px; /* Limits width for stacked items */
  }
  .featureItem {
    max-width: 100%; /* Take full width */
    justify-content: flex-start; /* Left-align item content */
    margin-bottom: 0; /* Remove bottom margin if present */
  }
  .iconCircle {
    width: 38px;
    height: 38px;
  }
  .featureIcon {
    font-size: 1.4rem;
  }
  .featureTitle {
    font-size: 1.1rem;
  }
  .featureDesc {
    font-size: 0.85rem;
  }
}

/* Small Mobile (max-width: 480px) */
@media (max-width: 480px) {
  .heroSection {
    min-height: 450px;
    padding: 40px 12px 20px;
  }
  .title {
    font-size: 1.8rem;
    margin-bottom: 20px;
  }
  .searchForm {
    margin-bottom: 30px;
  }
  .searchInput,
  .searchButton {
    padding: 12px 15px;
    font-size: 0.95rem;
  }
  .features {
    gap: 15px;
  }
  .featureItem {
    max-width: 100%;
  }
  .iconCircle {
    width: 32px;
    height: 32px;
    margin-right: 10px;
  }
  .featureIcon {
    font-size: 1.1rem;
  }
  .featureTitle {
    font-size: 1rem;
  }
  .featureDesc {
    font-size: 0.8rem;
  }
}

/* Very Small Mobile (max-width: 360px) */
@media (max-width: 360px) {
  .title {
    font-size: 1.6rem;
  }
  .searchInput,
  .searchButton {
    font-size: 0.9rem;
  }
  .featureTitle {
    white-space: normal; /* Allow title to wrap on very small screens */
  }
}
