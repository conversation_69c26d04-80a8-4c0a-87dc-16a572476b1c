/* Global styles reset - moved to globals.css */

/* Universal box-sizing */
.contentWrapper *,
.contentWrapper *::before,
.contentWrapper *::after {
  box-sizing: border-box;
}

.sectionBanner {
  background: linear-gradient(0deg, var(--primary-color) 0%, #e8e8e8 100%);
  padding: 120px 0 60px;
  position: relative;
  width: 100%;
}

.sectionBanner .bannerContent {
  text-align: left;
  margin-bottom: 40px;
}

.sectionBanner .bannerContent h1 {
  color: white;
  font-size: 3.5rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  text-align: left;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

/* Search Bar - nằm giữa banner và content */
.sectionBanner .searchBar {
  position: absolute;
  bottom: -30px; /* Đẩy search bar xuống để 1 nửa nằm dưới banner */
  left: 0;
  right: 0;
  z-index: 10;
}

.sectionBanner .searchForm {
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
  max-width: 900px;
  margin: 0 auto;
}

.sectionBanner .inputGroup {
  position: relative;
  flex: 1;
}

.sectionBanner .searchInput,
.sectionBanner .locationInput {
  width: 100%;
  padding: 24px 20px 12px 20px;
  border: 2px solid #e8e8e8;
  border-radius: 10px;
  font-size: 16px;
  outline: none;
  transition: all 0.3s ease;
  /* background-color: #f8f9fa; */
  height: 55px;
}

/* Floating Label Styles */
.sectionBanner .inputLabel {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  background-color: transparent;
  padding: 0 4px;
  z-index: 1;
}

/* Label khi input có focus hoặc có value */
.sectionBanner .searchInput:focus + .inputLabel,
.sectionBanner .locationInput:focus + .inputLabel,
.sectionBanner .searchInput:not(:placeholder-shown) + .inputLabel,
.sectionBanner .locationInput:not(:placeholder-shown) + .inputLabel {
  top: 8px;
  transform: translateY(0);
  font-size: 12px;
  color: var(--primary-color);
  font-weight: 600;
  background-color: white;
}

/* Đảm bảo placeholder ẩn khi có floating label */
.sectionBanner .searchInput::placeholder,
.sectionBanner .locationInput::placeholder {
  color: transparent;
}

.sectionBanner .searchIcon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 18px;
  pointer-events: none;
  z-index: 2;
}

/* Search Suggestions Dropdown */
.suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e8e8e8;
  border-top: none;
  border-radius: 0 0 10px 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.suggestionItem {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f5f5f5;
}

.suggestionItem:hover {
  background-color: #f8f9fa;
}

.suggestionItem:last-child {
  border-bottom: none;
}

.suggestionIcon {
  margin-right: 10px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
}

.suggestionText {
  font-size: 14px;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px; /* Giới hạn độ dài để tránh tràn viền */
}

.sectionBanner .searchButton {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-color) 100%
  );
  color: white;
  border: none;
  border-radius: 10px;
  padding: 15px 35px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 120px;
}

.sectionBanner .searchButton:hover {
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--secondary-color, #ed9f51) 50%,
    var(--accent-color, #f0a645) 100%
  );
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.sectionBanner .searchButton:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

@media (max-width: 992px) {
  .sectionBanner .bannerContent h1 {
    font-size: 2.8rem;
  }

  .searchForm {
    flex-direction: column;
    gap: 12px;
    padding: 20px;
  }

  .inputGroup {
    width: 100%;
  }

  .searchButton {
    width: 100%;
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .sectionBanner {
    padding: 60px 0 50px;
  }

  .sectionBanner .bannerContent h1 {
    font-size: 2.2rem;
  }

  .searchBar {
    bottom: -35px;
    left: 20px;
    right: 20px;
  }

  .searchForm {
    padding: 15px;
  }
}

/* Section Search  */

/* Section Find Jobs */
.sectionFind {
  padding: 60px 0 40px 0;
  background-color: #f8f9fa;
  /* background-color: white; */
  overflow-x: hidden !important; /* Ẩn scroll ngang thừa */
  width: 100%;
  max-width: 100vw; /* Đảm bảo không vượt quá viewport */
}

.totalJobs {
  /* text-align: center; */
  font-size: 24px;
  color: #333;
  font-weight: 500;
  margin-bottom: 20px;
  margin-left: 30px;
}

/* Loading animation cho số việc làm */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Counter animation effects */
.jobCount {
  color: var(--primary-color);
  font-weight: 700;
  font-size: 28px;
  display: inline-block;
  transition: transform 0.1s ease;
}

/* Thêm hiệu ứng nhảy khi số đang thay đổi */
.jobCount.counting {
  animation: numberJump 0.1s ease;
}

@keyframes numberJump {
  0% {
    transform: scale(1) translateY(0);
  }
  50% {
    transform: scale(1.05) translateY(-2px);
  }
  100% {
    transform: scale(1) translateY(0);
  }
}

/* Hiệu ứng glow khi hoàn thành */
.jobCount.completed {
  animation: completedGlow 0.5s ease;
}

@keyframes completedGlow {
  0% {
    text-shadow: 0 0 5px rgba(var(--primary-color-rgb, 74, 58, 117), 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(var(--primary-color-rgb, 74, 58, 117), 0.8);
    transform: scale(1.1);
  }
  100% {
    text-shadow: none;
    transform: scale(1);
  }
}

/* Responsive cho section find */
@media (max-width: 768px) {
  .sectionFind {
    padding: 40px 0 30px 0;
  }

  .totalJobs {
    font-size: 20px;
    padding: 0 20px;
  }

  .jobCount {
    font-size: 24px;
  }
}

/* Content Wrapper */
.contentWrapper {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  gap: 24px;
  padding: 0 20px;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
}

/* Layout Override - 25%-50%-25% */
.contentWrapper {
  display: flex;
  max-width: 100vw !important;
  margin: 0;
  gap: 0;
  padding: 0;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden !important; /* Ẩn scroll ngang */
}

.center {
  flex: 0 0 50% !important;
  width: 50% !important;
  background: transparent;
  border-radius: 0;
  min-height: 400px;
  padding: 0 20px;
}

/* Responsive cho layout mới */
@media (max-width: 992px) {
  .contentWrapper {
    flex-direction: column;
    gap: 20px;
    padding: 0 15px;
  }

  .leftSide,
  .center,
  .rightSide {
    width: 100% !important;
    flex: none !important;
    position: static;
    max-width: none;
  }

  .leftSide {
    order: 2;
  }

  .center {
    order: 1;
    padding: 0;
  }

  .rightSide {
    order: 3;
  }
}

/* Left Side - Filters */
.leftSide {
  flex: 0 0 25% !important;
  width: 25% !important;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  border-right: 1px solid #e8e8e8;
  padding: 0;
  height: fit-content;
  position: sticky;
  top: 20px;
}

.filterSection {
  padding: 0;
}

.filterHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 16px 20px;
  border-bottom: 1px solid #e8e8e8;
}

.filterHeader h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.contentWrapper .clearAll {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 14px;
  cursor: pointer;
  font-weight: 500;
}

.clearAll:hover {
  text-decoration: underline;
}

.filterGroup {
  border-bottom: 1px solid #f0f0f0;
  padding: 0;
}

.filterTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px 20px;
}

.filterTitle h4 {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin: 0;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.clearFilter {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 12px;
  cursor: pointer;
  font-weight: 500;
}

.clearFilter:hover {
  text-decoration: underline;
}

.filterOptions {
  padding: 0 20px 16px 20px;
}

.filterOption {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  cursor: pointer;
  border-radius: 8px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateY(0);
  animation: fadeInUp 0.3s ease;
}

/* Animation cho filter options */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effect cho tất cả options (trừ first-child) */
.filterOption:not(:first-child):hover {
  background-color: var(--last-color);
  transform: translateX(4px);
}

/* Hover effect cho first-child (All option) */
.filterOption:first-child:hover {
  background-color: #e8f2ff;
  transform: translateX(4px);
}

/* Option "All" - không có background mặc định */
.filterOption:first-child {
  border-radius: 6px;
}

.filterOption:first-child .optionName {
  color: #333;
  font-weight: 400;
}

/* All option khi được selected (không có filter nào được chọn) */
.filterOption:first-child.selected .optionName {
  color: var(--primary-color);
  font-weight: 600;
}

.optionName {
  font-size: 16px;
  color: #333;
  flex: 1;
}

/* Selected filter option */
.filterOption.selected {
  /* background-color: #f0f4ff !important; */
  /* border: 1px solid var(--primary-color); */
  border-radius: 6px;
}

.filterOption.selected .optionName {
  color: var(--primary-color) !important;
  font-weight: 600;
}

.filterOption.selected:hover {
  background-color: var(--last-color) !important;
  transform: translateX(2px);
}

.showMore {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 14px;
  cursor: pointer;
  margin-top: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 6px 12px;
  border-radius: 6px;
}

.showMore:hover {
  background-color: #f0f4ff;
  transform: translateY(-1px);
}

/* Center and Right Side */
.center {
  flex: 0 0 50%;
  width: 50%;
  background: transparent;
  border-radius: 12px;
  min-height: 400px;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
}

.rightSide {
  flex: 0 0 25% !important;
  width: 25% !important;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  border-left: 1px solid #e8e8e8;
  min-height: 400px;
  padding: 20px;
}

/* Responsive */
@media (max-width: 992px) {
  .contentWrapper {
    flex-direction: column;
    gap: 20px;
    padding: 0 15px;
  }

  .leftSide,
  .rightSide {
    width: 100%;
    position: static;
  }

  .leftSide {
    order: 2;
  }

  .center {
    order: 1;
  }

  .rightSide {
    order: 3;
  }
}

@media (max-width: 768px) {
  .contentWrapper {
    padding: 0 10px;
    gap: 15px;
  }

  .sectionFind {
    padding: 40px 0 30px 0;
  }

  .totalJobs {
    font-size: 20px;
    padding: 0 20px;
  }

  .jobCount {
    font-size: 24px;
  }

  .center {
    padding: 0 10px;
  }

  .resultsHeader {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .jobCard {
    padding: 20px;
  }

  .jobHeader {
    flex-direction: column;
    gap: 16px;
  }

  .jobMeta {
    flex-direction: column;
    gap: 12px;
  }

  .jobFooter {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .jobActions {
    width: 100%;
  }

  .applyBtn,
  .detailsBtn {
    flex: 1;
    text-align: center;
  }
}

/* Center Section - Job Listings */
.center {
  flex: 0 0 50% !important;
  width: 50% !important;
  background: transparent;
  border-radius: 0;
  min-height: 400px;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  height: auto; /* Thay đổi từ calc(100vh - 180px) thành auto để linh hoạt */
}

.jobsContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex-grow: 1; /* Allow jobsContainer to fill .center if .center has a defined height */
  min-height: 0;
}

/* Search Results Header */
.resultsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.resultsInfo {
  display: flex;
  align-items: center;
  gap: 30px;
}

.resultsCount {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.sortBy {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sortBy label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.sortSelect {
  padding: 8px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

/* Job Listings */
.jobsList {
  padding-top: 3px;
  overflow-y: auto; /* Enable vertical scroll */
  flex-grow: 1; /* Allow jobsList to take available space in jobsContainer */
  min-height: calc(190vh - 120px); /* Dài hơn để chạm đến profession filter */
  max-height: calc(190vh - 120px); /* Giới hạn chiều cao để tạo scroll */
}

.loadingJobs {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* Job Card */
.jobCard {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 16px;
}

.jobCard:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  /* border-color: #d1d5db; */
  border: 2px solid var(--primary-color);
  transform: translateY(-2px);
}

/* Job Header */
.jobHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.companyInfo {
  display: flex;
  gap: 7px;
  align-items: flex-start;
  flex: 1;
}

.companyLogo {
  width: 56px;
  height: 56px;
  border-radius: 8px;
  overflow: hidden;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.companyLogo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.companyInitial {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  color: #6b7280;
  background: #f3f4f6;
}

.jobDetails {
  flex: 1;
}

.jobDetails h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 6px 0;
  line-height: 1.4;
}

.companyName {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

.saveJob {
  background: none;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  font-weight: 500;
}

.saveJob:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: #f8f9ff;
}

/* Job Meta */
.jobMeta {
  display: flex;
  gap: 48px;
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;
}

.metaItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metaLabel {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metaValue {
  font-size: 15px;
  color: #374151;
  font-weight: 500;
}

/* Job Footer */
.jobFooter {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-top: 4px;
}

.postDate {
  font-size: 14px;
  color: #9ca3af;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 40px;
  padding: 20px 0;
}

.pageBtn {
  background: none;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pageBtn:hover:not(:disabled) {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.pageBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageNumbers {
  display: flex;
  gap: 5px;
}

.pageNumber {
  background: none;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 40px;
}

.pageNumber:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.pageNumber.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

/* Pagination Container */
.paginationContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
}

/* wrapper để căn giữa hoặc padding nếu cần */
.paginationWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px 0 0 0;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0; /* Không co lại */
}

/* Tất cả các rule sau sẽ apply lên các class của Ant Design
     nhờ cú pháp :global(...) trong CSS Module */

/* 1. Reset margin của root pagination */
.customPagination :global(.ant-pagination) {
  margin: 0;
}
/* 2. Style cho từng page item */
.customPagination :global(.ant-pagination-item),
.customPagination :global(.ant-pagination-item:focus),
.customPagination :global(.ant-pagination-item:focus-within) {
  border: 1px solid #e8e8e8 !important;
  border-radius: 6px !important;
  margin: 0 4px !important;
  background-color: white !important;
}
.customPagination :global(.ant-pagination-item a),
.customPagination :global(.ant-pagination-item:focus a),
.customPagination :global(.ant-pagination-item:focus-within a) {
  color: #666 !important;
}

/* 3. Hover states */
.customPagination :global(.ant-pagination-item:hover),
.customPagination :global(.ant-pagination-item:hover:focus) {
  border-color: var(--primary-color) !important;
  background-color: white !important;
}
.customPagination :global(.ant-pagination-item:hover a),
.customPagination :global(.ant-pagination-item:hover:focus a) {
  color: var(--primary-color) !important;
}

/* 4. Active page */
.customPagination :global(.ant-pagination-item-active),
.customPagination :global(.ant-pagination-item-active:focus),
.customPagination :global(.ant-pagination-item-active:hover) {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}
.customPagination :global(.ant-pagination-item-active a),
.customPagination :global(.ant-pagination-item-active:focus a),
.customPagination :global(.ant-pagination-item-active:hover a) {
  color: white !important;
}

/* 5. Prev / Next buttons */
.customPagination :global(.ant-pagination-prev),
.customPagination :global(.ant-pagination-next),
.customPagination :global(.ant-pagination-prev:focus),
.customPagination :global(.ant-pagination-next:focus) {
  border-radius: 6px !important;
  margin: 0 8px !important;
  background-color: transparent !important;
}
.customPagination :global(.ant-pagination-prev a),
.customPagination :global(.ant-pagination-next a),
.customPagination :global(.ant-pagination-prev:focus a),
.customPagination :global(.ant-pagination-next:focus a) {
  color: #666 !important;
}
.customPagination :global(.ant-pagination-prev:hover),
.customPagination :global(.ant-pagination-next:hover) {
  background-color: transparent !important;
}
.customPagination :global(.ant-pagination-prev:hover a),
.customPagination :global(.ant-pagination-next:hover a) {
  color: var(--primary-color) !important;
}

/* 6. Disabled states */
.customPagination :global(.ant-pagination-disabled),
.customPagination :global(.ant-pagination-disabled:hover),
.customPagination :global(.ant-pagination-disabled:focus) {
  border-color: #f0f0f0 !important;
  background-color: #f5f5f5 !important;
}
.customPagination :global(.ant-pagination-disabled a),
.customPagination :global(.ant-pagination-disabled:hover a),
.customPagination :global(.ant-pagination-disabled:focus a) {
  color: #ccc !important;
}

/* 7. Tổng số items text */
.customPagination :global(.ant-pagination-total-text) {
  color: #666 !important;
  font-size: 14px !important;
  margin-right: 16px !important;
}

/* Responsive cho center section */
@media (max-width: 768px) {
  .center {
    padding: 0 10px;
  }

  .resultsHeader {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .jobCard {
    padding: 20px;
  }

  .jobHeader {
    flex-direction: column;
    gap: 16px;
  }

  .jobMeta {
    flex-direction: column;
    gap: 12px;
  }

  .jobFooter {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .jobActions {
    width: 100%;
  }

  .applyBtn,
  .detailsBtn {
    flex: 1;
    text-align: center;
  }
}

/* Right Side Components */
.subscriptionWidget {
  /* background: white; */
  background: transparent;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
  /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
}

.subscriptionWidget h3 {
  font-size: 25px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.locationHighlight {
  color: var(--primary-color);
  font-weight: 600;
}

.emailInput {
  margin-bottom: 16px;
}

.emailField {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.emailField:focus {
  border-color: var(--primary-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.subscribeBtn {
  width: 100%;
  background: transparent;
  color: var(--primary-color);
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;
  border: 3px solid var(--primary-color);
}

.subscribeBtn:hover {
  background: var(--hover-color);
  transform: translateY(-1px);
}

.notInterested {
  font-size: 12px;
  color: #9ca3af;
  text-align: center;
  margin: 0;
}

.notInterested span {
  color: var(--primary-color);
  cursor: pointer;
  text-decoration: underline;
}

/* Popular Companies */
.popularCompanies {
  /* background: white; */
  background: transparent;
  border-radius: 12px;
  padding: 24px;
  /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
}

.popularCompanies h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.companiesList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.companyItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.companyItem:hover {
  background-color: var(--last-color);
  border: 2px solid var(--primary-color);
}

.companyItem .companyLogo {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.companyItem .companyInitial {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  background: #f3f4f6;
}

.companyItem .companyInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.companyItem .companyInfo h4 {
  font-size: 15px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 2px 0;
  line-height: 1.2;
}

.companyItem .companyInfo p {
  font-size: 13px;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
  line-height: 1.2;
}

.seeAllJobs {
  width: 100%;
  background: none;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.seeAllJobs:hover {
  border-color: var(--primary-color);
  background: #f8f9ff;
}

/* Salary Range Inputs */
.salaryInputs {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 12px;
}

.salaryInputs .inputGroup {
  flex: 1;
}

.salaryInput {
  width: 100% !important;
  height: 40px !important;
  border-radius: 6px !important;
  border: 1px solid #d1d5db !important;
  font-size: 14px !important;
}

.salaryInput:hover {
  border-color: var(--primary-color) !important;
}

.salaryInput:focus,
.salaryInput.ant-input-number-focused {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1) !important;
}

.separator {
  font-weight: 600;
  color: #6b7280;
  font-size: 16px;
}
