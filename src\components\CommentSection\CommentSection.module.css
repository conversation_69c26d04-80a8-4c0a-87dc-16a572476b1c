.commentSection {
  margin-top: 16px;
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
}

.commentsList {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 12px;
}

.commentItem {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  padding: 0 8px;
}

.commentAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  background: linear-gradient(135deg, #FF6701 0%, #FF8533 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatarIcon {
  color: white;
  font-size: 16px;
}

.commentContent {
  flex: 1;
  min-width: 0;
}

.commentBubble {
  background: #f0f2f5;
  padding: 8px 12px;
  border-radius: 16px;
  margin-bottom: 4px;
  max-width: fit-content;
}

.commentAuthor {
  font-weight: 600;
  font-size: 13px;
  color: #1c1e21;
  margin-bottom: 2px;
}

.commentText {
  font-size: 14px;
  color: #1c1e21;
  line-height: 1.4;
  word-wrap: break-word;
}

.commentImageContainer {
  margin-top: 8px;
}

.commentImage {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
  object-fit: cover;
}

.commentImage:hover {
  transform: scale(1.02);
}

.commentActions {
  display: flex;
  gap: 12px;
  align-items: center;
  padding-left: 12px;
}

.commentTime {
  font-size: 12px;
  color: #65676b;
}

.commentActionBtn {
  background: none;
  border: none;
  color: #65676b;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  padding: 2px 0;
  transition: color 0.2s ease;
}

.commentActionBtn:hover {
  color: #1c1e21;
  text-decoration: underline;
}

.addCommentForm {
  padding: 12px 8px 16px 8px;
  border-top: 1px solid #e4e6ea;
  margin-top: 8px;
}

.commentInputContainer {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.inputAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  background: linear-gradient(135deg, #FF6701 0%, #FF8533 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.inputWrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f0f2f5;
  border-radius: 20px;
  padding: 8px 8px 8px 12px;
  gap: 8px;
  position: relative;
  min-height: 36px;
}

.commentInput {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: #1c1e21;
  resize: none;
  min-height: 20px;
  max-height: 100px;
  overflow-y: auto;
  line-height: 1.4;
}

.commentInput::placeholder {
  color: #65676b;
}

.inputActions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.actionButton {
  background: none;
  border: none;
  color: #65676b;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 28px;
  height: 28px;
}

.actionButton:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #FF6701;
}

.submitBtn {
  background: none;
  border: none;
  color: #FF6701;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 28px;
  height: 28px;
}

.submitBtn:hover:not(:disabled) {
  background: rgba(255, 103, 1, 0.1);
  transform: scale(1.05);
}

.submitBtn:disabled {
  color: #bcc0c4;
  cursor: not-allowed;
  transform: none;
}

.submitBtn:active:not(:disabled) {
  transform: scale(0.95);
}

.hiddenFileInput {
  display: none;
}

.imagePreview {
  margin-top: 8px;
  position: relative;
  display: inline-block;
}

.previewImage {
  max-width: 200px;
  max-height: 150px;
  border-radius: 8px;
  object-fit: cover;
}

.removeImageBtn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emojiPicker {
  background: white;
  border: 1px solid #e4e6ea;
  border-radius: 8px;
  padding: 8px;
  margin: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 280px;
}

.emojiButton {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emojiButton:hover {
  background: #f0f2f5;
}

.spinIcon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #65676b;
}

.loadingIcon {
  font-size: 16px;
  animation: spin 1s linear infinite;
}

.noComments {
  text-align: center;
  padding: 20px;
  color: #65676b;
}

.noComments p {
  margin: 0;
  font-size: 14px;
}

.loginPrompt {
  text-align: center;
  padding: 16px;
  background: #f0f2f5;
  border-radius: 8px;
  margin: 8px;
}

.loginPrompt p {
  margin: 0;
  color: #65676b;
  font-size: 14px;
}

/* Reply styles */
.replyContainer {
  margin-top: 8px;
  padding-left: 12px;
  border-left: 2px solid #e4e6ea;
}

.replyInputContainer {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  margin-top: 8px;
}

.repliesContainer {
  margin-top: 12px;
  padding-left: 20px;
  border-left: 2px solid #e4e6ea;
}

.repliesContainer .commentItem {
  margin-bottom: 8px;
}

.repliesContainer .commentAvatar {
  width: 28px;
  height: 28px;
}

.repliesContainer .commentBubble {
  font-size: 13px;
}

.repliesContainer .commentActions {
  font-size: 11px;
}

.repliesContainer .commentTime {
  font-size: 11px;
}

.repliesContainer .commentActionBtn {
  font-size: 11px;
  padding: 1px 0;
}

/* Reply form styles */
.replyForm {
  margin-top: 8px;
  padding: 8px 0;
}

.replyInput {
  width: 100%;
  border: 1px solid #e4e6ea;
  border-radius: 16px;
  padding: 8px 12px;
  font-size: 13px;
  resize: none;
  outline: none;
  min-height: 36px;
  max-height: 100px;
}

.replyInput:focus {
  border-color: #FF6701;
}

.replyActions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.replyActionBtn {
  background: none;
  border: none;
  color: #65676b;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.replyActionBtn:hover {
  background: #f0f2f5;
  color: #1c1e21;
}

.replySubmitBtn {
  background: #FF6701;
  color: white;
  border: none;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.replySubmitBtn:hover:not(:disabled) {
  background: #e55a00;
}

.replySubmitBtn:disabled {
  background: #bcc0c4;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .commentItem {
    padding: 0 4px;
  }
  
  .addCommentForm {
    padding: 0 4px;
  }
  
  .commentText {
    font-size: 13px;
  }
  
  .commentInput {
    font-size: 13px;
  }
}
