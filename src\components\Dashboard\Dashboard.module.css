.dashboardContainer {
  display: flex;
  min-height: 100vh;
}

.mainContent {
  flex: 1;
  margin-left: 250px;
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
  transition: margin-left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mainContent.expanded {
  margin-left: 70px;
}

.contentWrapper {
  max-width: 1200px;
  margin: 0 auto;
  overflow: visible; /* Ensure dropdowns can appear outside */
}



/* Responsive cho mobile */
@media (max-width: 768px) {
  .dashboardContainer {
    flex-direction: column;
  }
  
  .mainContent {
    margin-left: 0;
    padding: 10px;
    padding-top: 80px;
    transition: margin-left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  
  .mainContent.expanded {
    margin-left: 0;
  }
  
  .contentWrapper {
    padding: 0;
  }
  
  .content {
    padding: 15px;
    margin-top: 10px;
  }
  
  .content h1 {
    font-size: 24px;
  }
  
  .content p {
    font-size: 14px;
  }
}

/* Hiệu ứng loading cho content */
.content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

/* Style cho các card con trong content */
.contentGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.contentCard {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: transform 0.2s ease;
}

.contentCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.contentWrapper {
  padding: 30px;
  max-width: 800px;
  margin: 0 auto;
}

.content {
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-top: 20px;
}

.content h1 {
  color: #333;
  margin-bottom: 15px;
  font-size: 24px;
  font-weight: 600;
}

.content p {
  color: #666;
  line-height: 1.6;
  font-size: 16px;
}

.postsSection {
  margin-top: 20px;
  overflow: visible; /* Ensure dropdowns can appear outside */
  position: relative;
}

/* Responsive adjustments for posts */
@media (max-width: 768px) {
  .contentWrapper {
    padding: 15px;
  }
  
  .postsSection {
    margin-top: 15px;
  }
}
