.logosSection {
  background: transparent;
  width: 100%;
  padding: 34px 0 14px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.heading {
  font-size: 1.3rem;
  font-weight: 600;
  color: #252c36;
  margin-bottom: 32px;
}

.logosRow {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 48px;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.logoItem {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
}

.logoItem img {
  max-height: 140px;
  max-width: 2900px;
  object-fit: contain;
  opacity: 0.86;
  transition: filter 0.18s;
}

.logoItem img:hover {
  filter: grayscale(0) contrast(1.1);
  opacity: 1;
}


/* --- Responsive Adjustments --- */

/* Điều chỉnh cho màn hình <PERSON>t (max-width: 992px) */
@media (max-width: 992px) {
  .logosSection {
    padding: 30px 0 10px 0; /* Gi<PERSON>m padding */
  }

  .container {
    padding: 0 20px; /* Giảm padding ngang một chút */
  }

  .heading {
    font-size: 1.2rem; /* Giảm kích thước tiêu đề */
    margin-bottom: 28px;
  }

  .logosRow {
    gap: 30px; /* Giảm khoảng cách giữa các logo */
  }

  .logoItem {
    min-width: 70px;
    max-width: 150px; /* Điều chỉnh kích thước tối đa của logo item */
  }

  .logoItem img {
    max-height: 35px; /* Giảm kích thước logo */
  }
}

/* Điều chỉnh cho màn hình điện thoại (max-width: 768px - thường dùng hơn 700px) */
@media (max-width: 768px) {
  .logosSection {
    padding: 25px 0 8px 0;
  }

  .container {
    padding: 0 16px; /* Padding ngang nhỏ hơn nữa */
  }

  .heading {
    font-size: 1.1rem; /* Giảm kích thước tiêu đề thêm */
    margin-bottom: 24px;
  }

  .logosRow {
    gap: 20px; /* Giảm khoảng cách giữa các logo */
    justify-content: center; /* Đảm bảo căn giữa */
  }

  .logoItem {
    min-width: 60px; /* Kích thước tối thiểu cho logo trên mobile */
    max-width: 120px; /* Kích thước tối đa cho logo trên mobile */
    /* Có thể dùng flex: 0 0 calc(33.33% - 20px) nếu muốn 3 cột */
    /* Hoặc flex: 0 0 calc(50% - 20px) nếu muốn 2 cột trên một số mobile */
  }

  .logoItem img {
    max-height: 30px; /* Kích thước logo trên điện thoại */
  }
}

/* Điều chỉnh cho màn hình điện thoại nhỏ (max-width: 480px) */
@media (max-width: 480px) {
  .logosSection {
    padding: 20px 0 5px 0;
  }

  .container {
    padding: 0 12px; /* Padding ngang rất nhỏ */
  }

  .heading {
    font-size: 1rem; /* Tiêu đề nhỏ hơn */
    margin-bottom: 20px;
  }

  .logosRow {
    gap: 15px; /* Giảm khoảng cách thêm */
  }

  .logoItem {
    min-width: 50px;
    max-width: 100px; /* Giới hạn kích thước logo trên điện thoại nhỏ */
    /* Có thể dùng flex: 0 0 calc(50% - 15px) nếu muốn 2 cột mạnh mẽ */
  }

  .logoItem img {
    max-height: 25px; /* Kích thước logo nhỏ nhất */
  }
}

/* Điều chỉnh cho màn hình điện thoại cực nhỏ (max-width: 320px) */
@media (max-width: 320px) {
  .logosRow {
    gap: 10px;
  }
  .logoItem img {
    max-height: 22px;
  }
}
