.whySection {
  background: transparent;
  padding: 40px 0 0 0;
}

.container {
  display: flex;
  max-width: 1180px;
  margin: 0 auto;
  gap: 54px;
  align-items: stretch;
}

.leftCard {
  flex: 1 1 340px;
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 6px 24px 0 #f2f3fa;
  padding: 48px 38px 42px 42px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 330px;
  max-width: 390px;
}

.leftCard h2 {
  font-size: 2.1rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #23222b;
}

.leftCard p {
  font-size: 1.06rem;
  color: #82839a;
  margin-bottom: 36px;
  line-height: 1.6;
}

.learnMore {
  color: var(--primary-color);
  font-weight: 600;
  text-decoration: none;
  font-size: 1rem;
  display: flex;
  align-items: center;
  margin-top: auto;
  transition: color 0.18s;
}
.learnMoreArrow {
  margin-left: 5px;
  font-size: 1.13em;
}

.learnMore:hover {
  color: var(--secondary-color);
}

.featuresList {
  flex: 2 1 480px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 34px 42px;
  padding: 14px 0 8px 0;
}

.featureItem {
  display: flex;
  align-items: flex-start;
  gap: 18px;
}

.iconWrap {
  width: 44px;
  height: 44px;
  background: var(--last-color);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--secondary-color);
  font-size: 28px;
  box-shadow: 0 1px 7px #f7e6f7;
  margin-top: 2px;
}

.featureItem h3 {
  font-size: 1.07rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: #23222b;
}

.featureItem p {
  font-size: 0.97rem;
  color: #82839a;
  margin: 0;
}

/* --- Responsive Adjustments --- */

/* Điều chỉnh cho màn hình Tablet lớn (max-width: 1100px) */
@media (max-width: 1100px) {
  .container {
    gap: 40px; /* Giảm khoảng cách */
    padding: 0 20px;
  }
  .leftCard {
    flex-basis: 40%; /* Điều chỉnh tỷ lệ flex-basis */
    max-width: 400px;
    padding: 40px 30px;
  }
  .leftCard h2 {
    font-size: 1.9rem;
  }
  .leftCard p {
    font-size: 1rem;
  }
  .featuresList {
    flex-basis: 55%;
    gap: 30px 30px; /* Giảm gap giữa các feature item */
  }
  .featureItem h3 {
    font-size: 1rem;
  }
  .featureItem p {
    font-size: 0.92rem;
  }
}

/* Điều chỉnh cho màn hình Tablet nhỏ / Điện thoại ngang (max-width: 950px - breakpoint hiện tại của bạn) */
@media (max-width: 950px) {
  .whySection {
    padding: 50px 0 0 0; /* Điều chỉnh padding */
  }
  .container {
    flex-direction: column; /* Xếp chồng leftCard và featuresList */
    gap: 32px;
    padding: 0 20px; /* Giữ padding ngang */
    align-items: center; /* Căn giữa các phần khi xếp chồng */
  }
  .leftCard {
    margin: 0 auto; /* Căn giữa card */
    max-width: 600px; /* Cho phép card rộng hơn trên mobile nếu màn hình lớn */
    width: 100%; /* Chiếm toàn bộ chiều rộng có sẵn */
    flex-basis: auto; /* Cho phép flex-basis tự động */
    padding: 36px 28px; /* Điều chỉnh padding card */
    text-align: center; /* Căn giữa văn bản trong card */
    align-items: center; /* Căn giữa nội dung */
  }
  .leftCard h2 {
    font-size: 1.8rem;
    margin-bottom: 12px;
  }
  .leftCard p {
    font-size: 1rem;
    margin-bottom: 24px;
  }
  .learnMore {
    margin-top: 18px; /* Điều chỉnh margin nút */
  }

  .featuresList {
    grid-template-columns: 1fr; /* Chuyển thành một cột duy nhất */
    gap: 28px; /* Giữ gap như bạn đề xuất */
    width: 100%; /* Chiếm toàn bộ chiều rộng */
    max-width: 600px; /* Giới hạn max-width cho danh sách tính năng */
    padding: 0; /* Bỏ padding vì container đã có */
  }
  .featureItem {
    flex-direction: row; /* Giữ icon và text trên cùng một hàng */
    align-items: flex-start; /* Căn trên cùng */
    gap: 15px; /* Giảm gap giữa icon và text */
    text-align: left; /* Đảm bảo văn bản featureItem vẫn căn trái */
  }
  .iconWrap {
    width: 40px;
    height: 40px;
    font-size: 24px;
    margin-top: 0; /* Đảm bảo icon căn trên cùng */
  }
  .featureItem h3 {
    font-size: 1rem;
  }
  .featureItem p {
    font-size: 0.9rem;
  }
}

/* Điều chỉnh cho màn hình Điện thoại nhỏ (max-width: 576px) */
@media (max-width: 576px) {
  .whySection {
    padding: 40px 0 0 0;
  }
  .container {
    gap: 24px;
    padding: 0 16px; /* Giảm padding ngang thêm */
  }
  .leftCard {
    padding: 30px 20px;
  }
  .leftCard h2 {
    font-size: 1.6rem;
    margin-bottom: 10px;
  }
  .leftCard p {
    font-size: 0.95rem;
    margin-bottom: 20px;
  }
  .featuresList {
    gap: 20px;
  }
  .featureItem {
    gap: 12px;
  }
  .iconWrap {
    width: 36px;
    height: 36px;
    font-size: 20px;
  }
  .featureItem h3 {
    font-size: 0.95rem;
  }
  .featureItem p {
    font-size: 0.85rem;
  }
}

/* Điều chỉnh cho màn hình Điện thoại rất nhỏ (max-width: 400px) */
@media (max-width: 400px) {
  .whySection {
    padding: 30px 0 0 0;
  }
  .container {
    gap: 20px;
    padding: 0 12px;
  }
  .leftCard {
    padding: 24px 18px;
  }
  .leftCard h2 {
    font-size: 1.4rem;
  }
  .leftCard p {
    font-size: 0.9rem;
  }
  .featuresList {
    gap: 18px;
  }
  .featureItem {
    flex-direction: column; /* Icon và text xếp chồng nếu quá hẹp */
    align-items: center; /* Căn giữa khi xếp chồng */
    text-align: center;
    gap: 8px; /* Giảm khoảng cách */
  }
  .iconWrap {
    margin-bottom: 0; /* Reset margin-top */
  }
}
