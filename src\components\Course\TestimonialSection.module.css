.testimonialSection {
  background: #fafafd;
  padding: 64px 0 32px 0;
  width: 100%;
}
.grid {
  display: flex;
  justify-content: center;
  gap: 56px;
  max-width: 1300px;
  margin: 0 580px;
}
.left {
  min-width: 320px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 28px;
}
.heading {
  font-size: 2rem;
  font-weight: 600;
  color: #16161a;
  line-height: 1.22;
  margin-bottom: 12px;
}
.checkAllBtn {
  background: var(--primary-color);
  color: #fff;
  padding: 11px 38px;
  border-radius: 7px;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: background 0.18s;
  margin-top: 18px;
  box-shadow: 0 2px 8px #e8e8f6;
}
.checkAllBtn:hover {
  background: var(--secondary-color);
}

.right {
  flex: 1;
  display: flex;
  align-items: center;
}
.slider {
  display: flex;
  align-items: center;
  gap: 18px;
}
.arrowBtn {
  border: none;
  background: #efeff2;
  color: #333;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  font-size: 22px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.18s;
  margin: 0 4px;
}
.arrowBtn:hover {
  background: #dadada;
}

.card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 28px #eaedf7b3;
  padding: 34px 36px;
  min-width: 380px;
  max-width: 450px;
  min-height: 210px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 18px;
  position: relative;
}

.userRow {
  display: flex;
  align-items: center;
  gap: 14px;
  margin-bottom: 10px;
}
.avatar {
  width: 46px;
  height: 46px;
  object-fit: cover;
  border-radius: 50%;
  background: #eee;
  border: 2px solid #ececec;
}
.name {
  font-size: 1.07rem;
  font-weight: 700;
  color: #181824;
}
.title {
  font-size: 0.97rem;
  color: #868686;
  margin-top: 3px;
}
.text {
  color: #30323c;
  font-size: 1rem;
  line-height: 1.5;
  position: relative;
  margin-top: 2px;
}
.quoteMark {
  font-size: 2.4rem;
  color: #dbdbe5;
  margin-right: 10px;
  vertical-align: sub;
}

/* --- Responsive Adjustments --- */

/* Màn hình lớn hơn 1300px: Đảm bảo container không dính vào cạnh nếu màn hình quá rộng */
@media (min-width: 1301px) {
  .grid {
    padding: 0; /* Bỏ padding ngang nếu màn hình quá lớn, để max-width quản lý */
  }
}


/* Điều chỉnh cho màn hình Tablet lớn (max-width: 1100px) */
@media (max-width: 1100px) {
  .grid {
    gap: 40px; /* Giảm khoảng cách */
    padding: 0 20px;
  }
  .left {
    min-width: 280px; /* Giảm min-width cho cột trái */
    max-width: 350px;
  }
  .heading {
    font-size: 1.8rem; /* Giảm kích thước tiêu đề */
  }
  .checkAllBtn {
    padding: 10px 30px; /* Giảm padding nút */
  }
  .card {
    min-width: 340px; /* Giảm min-width của card */
    max-width: 400px;
    padding: 30px 30px;
  }
}

/* Điều chỉnh cho màn hình Tablet nhỏ / Điện thoại ngang (max-width: 900px) */
@media (max-width: 900px) {
  .testimonialSection {
    padding: 40px 0 20px 0; /* Giảm padding tổng thể */
  }
  .grid {
    flex-direction: column; /* Xếp chồng cột trái và cột phải */
    gap: 32px; /* Khoảng cách giữa các phần xếp chồng */
    padding: 0 18px; /* Padding ngang cho mobile */
    align-items: center; /* Căn giữa các cột khi xếp chồng */
  }
  .left {
    width: 100%; /* Cột trái chiếm toàn bộ chiều rộng */
    min-width: unset; /* Bỏ min-width để linh hoạt hơn */
    max-width: 600px; /* Giới hạn chiều rộng tối đa khi xếp chồng */
    text-align: center; /* Căn giữa văn bản và nút */
  }
  .heading {
    font-size: 1.6rem; /* Giảm kích thước tiêu đề */
    margin-bottom: 18px;
  }
  .checkAllBtn {
    margin-top: 10px; /* Điều chỉnh margin nút */
  }
  .right {
    width: 100%; /* Cột phải chiếm toàn bộ chiều rộng */
    min-width: unset;
    justify-content: flex-start; /* Căn trái slider (hoặc center tùy thích) */
    overflow-x: auto; /* Quan trọng: Cho phép slider cuộn ngang nếu các card tràn */
    padding-bottom: 10px; /* Thêm padding dưới để thanh cuộn (nếu hiện) không che mất nội dung */
  }
  .slider {
    gap: 12px; /* Giảm khoảng cách giữa các phần tử trong slider */
    flex-wrap: nowrap; /* Đảm bảo các card không xuống dòng trong slider */
    padding: 0 4px; /* Padding nhẹ trong slider để card không quá sát mép */
  }
  .arrowBtn {
    display: none; /* Ẩn mũi tên điều hướng trên mobile, dùng cuộn ngang thay thế */
  }
  .card {
    min-width: 300px; /* Giảm min-width của card trên mobile */
    max-width: 380px; /* Giới hạn max-width để không quá to */
    padding: 28px 24px; /* Điều chỉnh padding card */
    gap: 15px;
  }
  .userRow {
    gap: 12px;
  }
  .avatar {
    width: 42px;
    height: 42px;
  }
  .name {
    font-size: 1rem;
  }
  .title {
    font-size: 0.9rem;
  }
  .text {
    font-size: 0.95rem;
  }
}

/* Điều chỉnh cho màn hình điện thoại nhỏ (max-width: 576px) */
@media (max-width: 576px) {
  .testimonialSection {
    padding: 30px 0 15px 0;
  }
  .grid {
    gap: 24px;
    padding: 0 12px; /* Giảm padding ngang thêm */
  }
  .heading {
    font-size: 1.4rem;
  }
  .card {
    min-width: 280px; /* Card nhỏ hơn nữa */
    max-width: 340px;
    padding: 24px 20px;
    gap: 12px;
  }
  .userRow {
    gap: 10px;
  }
  .avatar {
    width: 38px;
    height: 38px;
  }
  .name {
    font-size: 0.95rem;
  }
  .title {
    font-size: 0.85rem;
  }
  .text {
    font-size: 0.9rem;
  }
  .quoteMark {
    font-size: 2rem;
  }
}

/* Điều chỉnh cho màn hình điện thoại rất nhỏ (max-width: 400px) */
@media (max-width: 400px) {
  .testimonialSection {
    padding: 25px 0 10px 0;
  }
  .grid {
    gap: 20px;
    padding: 0 10px;
  }
  .heading {
    font-size: 1.25rem;
  }
  .card {
    min-width: 260px; /* Giảm min-width để phù hợp hơn với màn hình rất nhỏ */
    padding: 20px 15px;
    gap: 10px;
  }
  .avatar {
    width: 34px;
    height: 34px;
  }
}


