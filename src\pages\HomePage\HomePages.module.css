/* Global reset để tránh overflow - moved to globals.css */

/* Container chính */
.homePageContainer {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

/* Hero - section 1 */
.hero {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 2rem;
  padding: 124px 16px 24px;
  background: #201514;
  color: #fff;
  width: 100%;
  max-width: 100vw;
  overflow: hidden; /* Ngăn content tràn ra ngoài */
  box-sizing: border-box;
}
.hero .left {
  max-width: 100%;
  padding-left: 350px;
  width: 100%;
  box-sizing: border-box;
}
.hero .left h1 {
  font-size: 2.5rem;
  line-height: 1.2;
  margin-bottom: 1rem;
}
.hero .left p {
  color: #ccc;
  margin-bottom: 2rem;
}

.hero .searchBar {
  display: flex;
  max-width: 400px;
  margin-bottom: 0.5rem;
  border-radius: 2rem;
  overflow: hidden;
  background: #333;
}
.hero .searchBar input {
  flex: 1;
  border: none;
  padding: 0.75rem 1rem;
  background: transparent;
  color: #fff;
  font-size: 0.95rem;
}
.hero .searchBar input::placeholder {
  color: #bbb;
}
.hero .searchBar button {
  background: #ff7800;
  border: none;
  padding: 0 1.5rem;
  color: #fff;
  font-weight: bold;
  cursor: pointer;
}

.hero .domains {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: #aaa;
  margin-bottom: 2rem;
}
.hero .domains span {
  cursor: pointer;
}

.hero .ratings {
  display: flex;
  gap: 2rem;
}
.hero .rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}
.hero .rating svg {
  color: #ffb400;
}

.hero .right {
  display: flex;
  padding-left: 250px;
}
.hero .imageContainer {
  position: relative;
  border-radius: 1rem;
  overflow: visible;
}
.hero .imageContainer img {
  display: block;
  width: 100%;
  max-width: 400px;
  height: 420px;
  object-fit: cover; /* Crop ảnh nếu tỷ lệ không khớp */
  object-position: center; /* Canh giữa ảnh */
  border-radius: 1rem;
}
.hero .uptimeCard {
  position: absolute;
  bottom: 1rem;
  left: -75px;
  background: var(--secondary-color);
  color: #fff;
  padding: 1rem;
  border-radius: 0.75rem;
  width: 10rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
.hero .upHeader {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
}
.hero .percent {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}
.hero .barBg {
  background: rgba(255, 255, 255, 0.3);
  height: 0.5rem;
  border-radius: 0.25rem;
}
.hero .bar {
  height: 100%;
  background: #fff;
  border-radius: 0.25rem;
}

.hero .partners {
  grid-column: 1 / -1;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 2rem;
}
.hero .partners img {
  max-height: 2rem;
  opacity: 0.7;
  transition: opacity 0.2s;
  border-right: 2px solid #444;
  padding-right: 70px;
}
.hero .partners img:hover {
  opacity: 1;
}
/*End hero (end section 1) */

/* Section 2 - Experience with Number */
.statsSection {
  background-color: #f7f6ff;
  padding: 60px 20px;
}

.statsSection .statsContainer {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

/* ---- Phần text bên trái ---- */
.statsSection .statsText {
  flex: 1 1 300px;
}

.statsSection .statsSubtitle {
  font-size: 16px;
  color: #7a7a7a;
  margin: 0 0 8px;
}

.statsSection .statsTitle {
  font-size: 30px;
  line-height: 1.2;
  margin: 0;
  color: var(--primary-color);
}

/* ---- Các thống kê bên phải ---- */
.statsSection .statsCards {
  display: flex;
  flex: 2 1 600px;
  justify-content: space-between;
  gap: 32px;
  flex-wrap: wrap;
}

.statsSection .statsCard {
  text-align: left;
  flex: 1 1 150px;
  /* border phân tách giữa các card */
  border-right: 1px solid #ddd;
  padding-right: 24px;
}

/* Bỏ border của card cuối cùng */
.statsSection .statsCard:last-child {
  border-right: none;
}

.statsSection .statsNumber {
  font-size: 36px;
  margin: 0;
  color: var(--primary-color);
}

.statsSection .statsPercent {
  font-size: 32px;
}

.statsSection .statsDesc {
  font-size: 14px;
  color: #7a7a7a;
  line-height: 1.4;
  margin-top: 8px;
}
/* End section 2 */

/* Responsive design */
@media (max-width: 1200px) {
  .hero .left {
    padding-left: 100px;
  }
}

@media (max-width: 968px) {
  .hero {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 3rem;
  }

  .hero .left {
    padding-left: 0;
    order: 1;
  }

  .hero .right {
    order: 2;
  }

  .hero .partners {
    order: 3;
    flex-wrap: wrap;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 16px 12px;
  }

  .hero .left h1 {
    font-size: 2rem;
  }

  .hero .imageContainer img {
    max-width: 300px;
    height: 320px;
  }

  .hero .uptimeCard {
    left: -50px;
    width: 8rem;
  }

  .hero .partners img {
    max-height: 1.5rem;
    padding-right: 30px;
  }
}
