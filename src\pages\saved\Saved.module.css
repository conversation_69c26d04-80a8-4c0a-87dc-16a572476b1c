.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.pageWrapper {
  display: flex;
  min-height: calc(100vh - 80px);
  padding-top: 80px;
}

.mainContent {
  flex: 1;
  margin-left: 250px;
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 80px);
  transition: margin-left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mainContent.expanded {
  margin-left: 70px;
}

.contentWrapper {
  max-width: 800px;
  margin: 0 auto;
}

.header {
  margin-bottom: 30px;
  text-align: center;
}

.header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 10px;
}

.header p {
  font-size: 1rem;
  color: #666;
  margin: 0;
}

.loadingWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  text-align: center;
}

.loadingWrapper p {
  margin-top: 15px;
  color: #666;
  font-size: 16px;
}

.postsSection {
  margin-top: 20px;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.emptyState p {
  margin-top: 15px;
  color: #666;
  font-size: 16px;
}

.notAuthenticated {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 160px);
  text-align: center;
}

.notAuthenticated h2 {
  color: #666;
  font-size: 1.5rem;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .mainContent {
    margin-left: 0;
    padding: 15px;
  }
  
  .mainContent.expanded {
    margin-left: 0;
  }
  
  .contentWrapper {
    max-width: 100%;
  }
  
  .header h1 {
    font-size: 1.5rem;
  }
  
  .header p {
    font-size: 0.9rem;
  }
}
