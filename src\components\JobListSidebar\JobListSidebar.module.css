/* components/JobListSidebar/JobListSidebar.module.css */
.sidebar {
  display: flex;
  flex-direction: column;
}

.headerPill {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f0f0f0;
  border-radius: 20px;
  margin-bottom: 12px;
}
.title {
  font-weight: 600;
  font-size: 14px;
}
.count {
  font-size: 14px;
  color: #555;
}

.jobList {
  list-style: none;
  margin: 0;
  padding: 0;
}
.jobCard {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s, border-left 0.2s;
}
.jobCard:hover {
  background-color: #f9f9f9;
}
.jobCard.active {
  background-color: #eef6ff;
  border-left: 4px solid #4a90e2;
}

.cardHeader {
  display: flex;
  align-items: center;
}
.logoWrapper {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f0f0f0;
}
.companyLogo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.jobInfo {
  margin-left: 8px;
  flex: 1;
}
.companyName {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}
.positionTitle {
  margin: 2px 0;
  font-size: 13px;
  color: #333;
}
.location {
  margin: 0;
  font-size: 12px;
  color: #666;
}
.bookmarkBtn {
  background: transparent;
  border: none;
  font-size: 16px;
  color: #888;
  margin-left: 8px;
}
.bookmarkBtn:hover {
  color: #333;
}

.badgesRow {
  display: flex;
  gap: 4px;
  margin-top: 6px;
}
.badgePerfect {
  background-color: #ffd700;
  color: #333;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
}
.badgeNew {
  background-color: #ff4081;
  color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
}

.tagsRow {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-top: 8px;
}
.tag {
  background-color: #e0e0e0;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  color: #333;
}

.footerRow {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}
.timeAgo {
  font-size: 11px;
  color: #999;
}
