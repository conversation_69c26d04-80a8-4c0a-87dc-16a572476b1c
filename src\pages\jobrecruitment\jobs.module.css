.jobsPage {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.pageHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0 40px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.pageHeader::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.3;
}

.headerContent {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.pageTitle {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.titleIcon {
  font-size: 52px;
}

.pageDescription {
  font-size: 18px;
  margin: 0 0 32px 0;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.postJobBtn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
  height: 48px;
  padding: 0 32px;
  border-radius: 24px;
  font-size: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.postJobBtn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.pageContent {
  max-width: 1200px;
  margin: -40px auto 0;
  padding: 0 20px 40px;
  position: relative;
  z-index: 1;
}

.searchSection {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: none;
  margin-bottom: 32px;
  overflow: hidden;
}

.searchSection .ant-card-body {
  padding: 32px;
}

.searchRow {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
}

.searchInput {
  flex: 1;
}

.searchInput .ant-input-search {
  border-radius: 12px;
  overflow: hidden;
}

.searchInput .ant-input {
  border-radius: 12px 0 0 12px;
  border-right: none;
  font-size: 16px;
  padding: 12px 16px;
}

.searchInput .ant-btn {
  border-radius: 0 12px 12px 0;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
}

.filterToggleBtn {
  height: 48px;
  border-radius: 12px;
  border: 2px solid #e6f7ff;
  color: #1890ff;
  font-weight: 600;
  min-width: 100px;
}

.filterToggleBtn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.filtersSection {
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filterGroup label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.filterGroup .ant-select-selector {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  height: 40px;
  transition: all 0.3s ease;
}

.filterGroup .ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.filterActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.filterActions .ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

.resultsSection {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 32px;
  min-height: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pageTitle {
    font-size: 32px;
    flex-direction: column;
    gap: 8px;
  }

  .titleIcon {
    font-size: 36px;
  }

  .pageDescription {
    font-size: 16px;
  }

  .pageContent {
    margin-top: -20px;
    padding: 0 16px 20px;
  }

  .searchSection .ant-card-body {
    padding: 20px;
  }

  .searchRow {
    flex-direction: column;
    gap: 12px;
  }

  .searchInput,
  .filterToggleBtn {
    width: 100%;
  }

  .resultsSection {
    padding: 20px;
  }

  .filterActions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .pageHeader {
    padding: 40px 0 30px;
  }

  .pageTitle {
    font-size: 28px;
  }

  .pageDescription {
    font-size: 14px;
  }

  .postJobBtn {
    height: 44px;
    font-size: 14px;
    padding: 0 24px;
  }

  .searchSection .ant-card-body {
    padding: 16px;
  }

  .filtersSection {
    padding-top: 16px;
  }

  .resultsSection {
    padding: 16px;
    margin: 0 -16px;
    border-radius: 0;
    box-shadow: none;
    border-top: 1px solid #f0f0f0;
  }
}
