.profileContainer {
  max-width: 900px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.coverPhotoSection {
  position: relative;
  height: 300px;
}

.coverPhoto {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coverPhotoUpload {
  position: absolute;
  bottom: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease;
}

.coverPhotoUpload:hover {
  background: rgba(0, 0, 0, 0.8);
}

.avatarSection {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  z-index: 2;
}

.avatar {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  border: 5px solid white;
  background: linear-gradient(135deg, #FF6701 0%, #FF8533 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatarIcon {
  font-size: 60px;
  color: white;
}

.avatarUpload {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: #FF6701;
  color: white;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.avatarUpload:hover {
  background: #e55a00;
  transform: scale(1.1);
}

.nameSection {
  background: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 8px;
}

.displayName {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #1c1e21;
}

.bio {
  font-size: 16px;
  color: #65676b;
  margin: 0;
  line-height: 1.4;
}

.actionButtons {
  padding: 80px 24px 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.editButton {
  background: #FF6701;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.editButton:hover {
  background: #e55a00;
  transform: translateY(-1px);
}

.editActions {
  display: flex;
  gap: 12px;
}

.saveButton {
  background: #42b883;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.saveButton:hover:not(:disabled) {
  background: #369870;
  transform: translateY(-1px);
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.cancelButton {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.cancelButton:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-1px);
}

.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.profileInfo {
  padding: 0 24px 24px;
}

.infoSection {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
}

.sectionTitle {
  font-size: 20px;
  font-weight: 700;
  color: #1c1e21;
  margin: 0 0 24px 0;
  border-bottom: 2px solid #FF6701;
  padding-bottom: 8px;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.infoField {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e4e6ea;
  transition: all 0.2s ease;
}

.infoField:hover {
  border-color: #FF6701;
  box-shadow: 0 2px 8px rgba(255, 103, 1, 0.1);
}

.fieldLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #65676b;
  margin-bottom: 8px;
}

.fieldLabel svg {
  color: #FF6701;
  font-size: 16px;
}

.fieldValue {
  font-size: 16px;
  color: #1c1e21;
  line-height: 1.4;
}

.fieldValue a {
  color: #FF6701;
  text-decoration: none;
}

.fieldValue a:hover {
  text-decoration: underline;
}

.fieldInput, .fieldSelect {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  color: #1c1e21;
  background: white;
  transition: all 0.2s ease;
}

.fieldInput:focus, .fieldSelect:focus {
  outline: none;
  border-color: #FF6701;
  box-shadow: 0 0 0 3px rgba(255, 103, 1, 0.1);
}

.fieldTextarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  color: #1c1e21;
  background: white;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
  transition: all 0.2s ease;
}

.fieldTextarea:focus {
  outline: none;
  border-color: #FF6701;
  box-shadow: 0 0 0 3px rgba(255, 103, 1, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .profileContainer {
    margin: 0;
    border-radius: 0;
  }
  
  .coverPhotoSection {
    height: 200px;
  }
  
  .avatar {
    width: 120px;
    height: 120px;
  }
  
  .avatarIcon {
    font-size: 48px;
  }
  
  .displayName {
    font-size: 24px;
  }
  
  .actionButtons {
    padding: 60px 16px 16px;
  }
  
  .profileInfo {
    padding: 0 16px 16px;
  }
  
  .infoSection {
    padding: 16px;
  }
  
  .infoGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .editActions {
    flex-direction: column;
    width: 100%;
  }
  
  .saveButton, .cancelButton, .editButton {
    width: 100%;
    justify-content: center;
  }
}
