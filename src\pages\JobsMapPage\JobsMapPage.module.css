/* pages/jobs-map/JobsMapPage.module.css */
.pageWrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
}
.main {
  display: flex;
  flex: 1;
  overflow: hidden;
}
.leftPane {
  flex: 0 0 35%;
  max-width: 400px;
  background-color: #fafafa;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 16px;
  box-sizing: border-box;
}
.searchForm {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}
.searchInput {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}
.searchSelect {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}
.searchBtn {
  padding: 8px 16px;
  background-color: #4a90e2;
  border: none;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}
.searchBtn:hover {
  background-color: #357ab8;
}
.statusMessage {
  font-size: 14px;
  color: #555;
  margin: 8px 0;
}
.statusMessageError {
  font-size: 14px;
  color: #d9534f;
  margin: 8px 0;
}
.rightPane {
  flex: 1;
  position: relative;
}
.mapOverlay,
.mapOverlayError {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.7);
  z-index: 10;
  font-size: 16px;
  color: #333;
}
.mapOverlayError {
  color: #d9534f;
}
/* Responsive: */
@media (max-width: 768px) {
  .main {
    flex-direction: column;
  }
  .leftPane,
  .rightPane {
    flex: none;
    width: 100%;
    max-width: none;
    height: auto;
  }
  .rightPane {
    height: 300px;
  }
}
