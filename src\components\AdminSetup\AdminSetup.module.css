.container {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.container h3 {
  color: #1f2937;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.container p {
  color: #6b7280;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.warning {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 1rem;
  color: #92400e !important;
  font-weight: 500;
}

.adminButton {
  background: linear-gradient(135deg, #ff6701, #ff8534);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 1rem 0;
  display: block;
  width: 100%;
}

.sampleButton {
  background: linear-gradient(135deg, #10b981, #34d399);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 1rem 0;
  display: block;
  width: 100%;
}

.adminButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #e55a01, #ff6701);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 103, 1, 0.3);
}

.sampleButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669, #10b981);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.adminButton:disabled,
.sampleButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.message {
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  font-weight: 500;
}

.success {
  background: #d1fae5;
  border: 1px solid #10b981;
  color: #065f46;
}

.error {
  background: #fee2e2;
  border: 1px solid #ef4444;
  color: #991b1b;
}

.instructions {
  background: #f8fafc;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 2rem;
}

.instructions h4 {
  color: #1f2937;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.instructions ol {
  color: #4b5563;
  line-height: 1.8;
  padding-left: 1.5rem;
}

.instructions li {
  margin-bottom: 0.5rem;
}
