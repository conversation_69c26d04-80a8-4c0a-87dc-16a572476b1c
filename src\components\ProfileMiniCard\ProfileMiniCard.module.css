.miniCard {
  width: 320px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  animation: fadeInUp 0.2s ease-out;
  position: fixed; /* Use fixed positioning for better control */
  z-index: 9999; /* Higher z-index to ensure it appears above other elements */
  max-height: 90vh; /* Prevent card from being too tall */
  overflow-y: auto; /* Allow scrolling if content is too long */
  pointer-events: auto; /* Ensure card can receive mouse events */
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.coverPhoto {
  position: relative;
  height: 80px;
  overflow: hidden;
}

.coverImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.defaultCover {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.profileInfo {
  position: relative;
  padding: 12px 16px;
}

.avatar {
  position: relative;
  top: 0;
  left: 0;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid white;
  overflow: hidden;
  background: white;
  flex-shrink: 0;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.defaultAvatar {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatarIcon {
  font-size: 24px;
  color: #9ca3af;
}

.userInfo {
  margin-top: 0;
  margin-left: 0;
}

.topSection {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.userDetails {
  flex: 1;
}

.userName {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.verifiedBadge {
  color: #3b82f6;
  font-size: 16px;
}

.userTitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.location {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 8px;
}

.locationIcon {
  font-size: 14px;
}

.bio {
  font-size: 14px;
  color: #374151;
  line-height: 1.4;
  margin: 8px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.stats {
  display: flex;
  justify-content: space-evenly;
  gap: 12px;
  margin: 12px 0 8px 0;
  padding: 8px 0;
  border-top: 1px solid #f3f4f6;
  border-bottom: 1px solid #f3f4f6;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.statNumber {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.statLabel {
  font-size: 12px;
  color: #6b7280;
}

.actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.actionBtn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.follow {
  background: #3b82f6;
  color: white;
}

.follow:hover {
  background: #2563eb;
}

.following {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.following:hover {
  background: #fee2e2;
  color: #dc2626;
  border-color: #fca5a5;
}

.message {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.message:hover {
  background: #e5e7eb;
}

.actionIcon {
  font-size: 16px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #6b7280;
}

.loadingIcon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .miniCard {
    width: 280px;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .actionBtn {
    width: 100%;
  }
}
