@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");

:root {
  /* 
  --primary-color: #6D2323;
  --secondary-color: #A31D1D;
  --hover-color: #E5D0AC;
  --last-color: #FEF9E1;
  --bg-color: #000000;
} */

  --primary-color: #ff6701;
  --secondary-color: #fea82f;
  --hover-color: #ffc288;
  --last-color: #fcecdd;
}

/* @media (prefers-color-scheme: dark) {
  :root {
    
  }
} */

html,
body {
  overflow-x: hidden !important;
  max-width: 100vw;
  box-sizing: border-box;
  height: 100%;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font-family: "Roboto", sans-serif;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  width: 100%;
}

/* Khi ≥576px */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

/* Khi ≥768px */
@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

/* Khi ≥992px */
@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

/* Khi ≥1200px */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

/* Khi ≥1400px */
@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/* Ngăn scroll kép */
html,
body {
  overflow-x: hidden;
}

/* Quy tắc quan trọng: ngăn scroll kép */
.container,
.leftPane,
.rightPane,
.jobList,
.chatBody,
[class*="container"],
[class*="pane"],
[class*="list"],
[class*="body"] {
  scrollbar-width: thin; /* Firefox */
}

/* Chỉ hiển thị scrollbar khi cần thiết */
.container:not(:hover)::-webkit-scrollbar,
.leftPane:not(:hover)::-webkit-scrollbar,
.rightPane:not(:hover)::-webkit-scrollbar,
.jobList:not(:hover)::-webkit-scrollbar,
.chatBody:not(:hover)::-webkit-scrollbar,
[class*="container"]:not(:hover)::-webkit-scrollbar,
[class*="pane"]:not(:hover)::-webkit-scrollbar,
[class*="list"]:not(:hover)::-webkit-scrollbar,
[class*="body"]:not(:hover)::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

/* Hiển thị scrollbar khi hover */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

/* Global styles moved from CSS Modules files */
html, body {
  overflow-x: hidden !important;
  max-width: 100vw;
  box-sizing: border-box;
  height: 100%;
}

/* Ant Design Tabs global styles */
.ant-tabs-tab {
  font-size: 16px !important;
  padding: 12px 0 !important;
  color: var(--primary-color) !important;
  margin-right: 20px !important;
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: var(--secondary-color) !important;
  font-weight: bold !important;
}

.ant-tabs-ink-bar {
  background-color: var(--secondary-color) !important;
  height: 3px !important;
}

.ant-tabs-tab:hover {
  color: var(--primary-color) !important;
}

.ant-tabs-nav::before {
  border-bottom: none !important;
}

.ant-tabs-nav {
  border-bottom: 1px solid #eaeaea !important;
  margin-bottom: 30px !important;
}

/* Ant Design Select global styles */
.ant-select {
  border-radius: 8px !important;
}

.ant-select .ant-select-selector {
  border: 2px solid #d9d9d9 !important;
  border-radius: 8px !important;
  height: 40px !important;
  padding: 0 12px !important;
  background-color: white !important;
  transition: all 0.3s ease !important;
  justify-content: center !important;
  align-items: center !important;
}

.ant-select:hover .ant-select-selector {
  border-color: var(--primary-color) !important;
}

.ant-select.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px rgba(255, 103, 1, 0.2) !important;
}

.ant-select .ant-select-selection-item {
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
  font-weight: 500 !important;
}

.ant-select-arrow .anticon {
  transition: transform 0.3s ease;
}

.ant-select-open .ant-select-arrow .anticon {
  transform: rotate(180deg);
}

.ant-select-arrow {
  color: var(--primary-color) !important;
  font-size: 12px !important;
  top: 50% !important;
  margin-top: -6px !important;
  right: 11px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding-top: 7px !important;
}

.ant-select-dropdown {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e6e6e6 !important;
}

.ant-select-item {
  padding: 8px 12px !important;
  font-size: 14px !important;
  transition: background-color 0.2s ease !important;
}

.ant-select-item:hover:not(.ant-select-item-option-selected) {
  background-color: rgba(255, 103, 1, 0.08) !important;
  color: var(--primary-color) !important;
  border-radius: 8px !important;
}

.ant-select-item.ant-select-item-option-selected {
  background-color: var(--primary-color) !important;
  color: white !important;
  font-weight: 600 !important;
}

.ant-select-item.ant-select-item-option-selected:hover {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.ant-select-item:not(.ant-select-item-option-selected) {
  background-color: white !important;
  color: #333 !important;
}

